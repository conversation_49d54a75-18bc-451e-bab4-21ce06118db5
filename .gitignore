
# Python bytecode
__pycache__/
*.py[cod]
*$py.class

# ROS 2 build artifacts
build/
install/
log/

# State files
.ros/states/
.ros/orchestrator_states/
*.jsonl
*_state.json

# Editor files
*~
.*.swp
.DS_Store

# Environment files
.env
.venv
env/
venv/
ENV/

# Distribution / packaging
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Colcon build artifacts
build/
install/
log/

