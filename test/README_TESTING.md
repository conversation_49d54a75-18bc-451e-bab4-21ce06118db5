# Orchestration State Machine Testing Guide

This guide explains how to test the orchestration state machine functionality and task queue system.

## Overview

The testing suite includes:
1. **Automated Test Suite** - Comprehensive scenarios testing all state transitions
2. **Manual Testing Tool** - Interactive testing for specific scenarios
3. **State Monitor** - Real-time status updates for the Physical AI team
4. **Launch Files** - Easy setup for testing environments

## Test Components

### 1. Orchestration Tester (`test_orchestration.py`)
Automated test suite that runs multiple scenarios:
- ✅ Single task success (PENDING → ACTIVE → PICKING → WAITING_FOR_DELIVERY → DELIVERING → COMPLETE)
- ❌ Pick failure (PENDING → ACTIVE → PICKING → FAILED)
- ❌ Object not found (PENDING → ACTIVE → stays ACTIVE)
- 📋 Multiple tasks in queue
- 🔄 State transition verification

### 2. State Monitor (`state_monitor_node.py`)
Publishes system status to `/physical_ai/system_status` for the Physical AI team:
```json
{
  "system_status": "PICKING_OBJECT",
  "current_task": {
    "id": "0",
    "status": "PICKING",
    "started_at": "2024-01-15T10:30:00"
  },
  "task_queue_length": 2,
  "robot_busy": true,
  "navigation_busy": false,
  "last_update": "2024-01-15T10:30:15",
  "recent_activities": [...]
}
```

### 3. Manual Tester (`manual_test_orchestration.py`)
Interactive testing tool with menu options:
- Send object requests
- Send destination requests
- Send complete tasks
- Simulate failures
- Test multiple tasks
- Monitor real-time status

## Running Tests

### Quick Start - Automated Testing

1. **Build the package:**
```bash
cd ~/Dev/creova-state-machine
colcon build --packages-select creova_state_machine
source install/setup.bash
```

2. **Run automated tests:**
```bash
ros2 launch creova_state_machine test_orchestration.launch.py
```

This launches:
- Orchestration node
- State monitor node
- Automated test suite
- Destination server (for testing)

### Manual Testing

1. **Launch the system:**
```bash
ros2 launch creova_state_machine test_orchestration.launch.py
```

2. **In a new terminal, run manual tester:**
```bash
source install/setup.bash
ros2 run creova_state_machine manual_test_orchestration
```

3. **Use the interactive menu to test scenarios**

### Monitor Physical AI Status

In a separate terminal, monitor the status updates:
```bash
ros2 topic echo /physical_ai/system_status
```

## Test Scenarios

### Scenario 1: Single Successful Task
1. Send object request: "apple"
2. Send destination request: "kitchen"
3. Verify state transitions:
   - PENDING → ACTIVE (object detection starts)
   - ACTIVE → PICKING (object found, manipulation starts)
   - PICKING → WAITING_FOR_DELIVERY (pick completed)
   - WAITING_FOR_DELIVERY → DELIVERING (navigation starts)
   - DELIVERING → COMPLETE (navigation finished)

### Scenario 2: Multiple Tasks Queue
1. Send first task: "apple" → "kitchen"
2. Send second task: "bottle" → "living_room"
3. Verify:
   - First task processes completely
   - Second task waits in queue
   - Queue length updates correctly
   - Tasks process sequentially

### Scenario 3: Pick Failure
1. Send task request
2. Simulate pick failure
3. Verify:
   - Task transitions to FAILED state
   - Robot becomes available for next task
   - Error is logged and reported

### Scenario 4: Object Not Found
1. Send request for non-existent object
2. Verify:
   - Task stays in ACTIVE state
   - System continues searching
   - No manipulation command sent

## Key Topics for Monitoring

### Input Topics (for testing):
- `/latest_object` - Object requests from Physical AI
- `/latest_destination` - Destination requests from Physical AI
- `/perception/object_list` - Available objects from perception
- `/robot_status` - Manipulation results
- `/nav2_status` - Navigation status

### Output Topics (for monitoring):
- `/state_changes` - All state transitions
- `/manipulation/command` - Commands to manipulation team
- `/physical_ai/system_status` - Status for Physical AI team

### Services:
- `get_destination` - Destination lookup service

## Expected State Flow

```
Task Creation:
[Object Request] + [Destination Request] → Task Created → PENDING

State Machine Flow:
PENDING → ACTIVE → PICKING → WAITING_FOR_DELIVERY → DELIVERING → COMPLETE
    ↓        ↓         ↓              ↓                ↓
  Queue    Search   Manipulate    Ready           Navigate
```

## Debugging Tips

1. **Check logs:** All nodes provide detailed logging
2. **Monitor state changes:** `ros2 topic echo /state_changes`
3. **Verify object list:** `ros2 topic echo /perception/object_list`
4. **Check queue status:** Monitor `/physical_ai/system_status`

## State Persistence

The orchestration node saves state to JSON files in:
```
~/.ros/orchestrator_states/tasks/
```

Each task is saved as `task_<id>.json` with complete state information.

## Integration Notes

- **Perception Team:** Publishes to `/perception/object_list`
- **Manipulation Team:** Subscribes to `/manipulation/command`, publishes to `/robot_status`
- **Navigation Team:** Provides `get_destination` service, publishes to `/nav2_status`
- **Physical AI Team:** Publishes to `/latest_object` and `/latest_destination`, subscribes to `/physical_ai/system_status`

## Troubleshooting

### Common Issues:
1. **No state transitions:** Check if orchestration node is running
2. **Tasks not created:** Verify both object and destination are sent
3. **Pick not starting:** Check if object exists in perception list
4. **Navigation not starting:** Verify destination service is available

### Logs to Check:
- Orchestration node logs for state machine execution
- State monitor logs for status updates
- Test node logs for scenario execution
