<package>

  <name>simple_grasping</name>
  <version>0.4.0</version>
  <description>
    Basic grasping applications and demos.
  </description>
  <author><PERSON></author>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <license>BSD</license>
  <url>http://ros.org/wiki/simple_grasping</url>
  
  <buildtool_depend>catkin</buildtool_depend>

  <build_depend>actionlib</build_depend>
  <build_depend>cmake_modules</build_depend>
  <build_depend>geometry_msgs</build_depend>
  <build_depend>grasping_msgs</build_depend>
  <build_depend>message_generation</build_depend>
  <build_depend>moveit_msgs</build_depend>
  <build_depend>pcl_ros</build_depend>
  <build_depend>roscpp</build_depend>
  <build_depend>sensor_msgs</build_depend>
  <build_depend>shape_msgs</build_depend>
  <build_depend>tf</build_depend>

  <!-- Needed to work around https://bugs.debian.org/cgi-bin/bugreport.cgi?bug=894656 on Debian Stretch -->
  <build_depend>libvtk-qt</build_depend>
  <run_depend>libvtk-qt</run_depend>
  
  <run_depend>actionlib</run_depend>
  <run_depend>geometry_msgs</run_depend>
  <run_depend>grasping_msgs</run_depend>
  <run_depend>moveit_python</run_depend>
  <run_depend>message_runtime</run_depend>
  <run_depend>moveit_msgs</run_depend>
  <run_depend>pcl_ros</run_depend>
  <run_depend>roscpp</run_depend>
  <run_depend>sensor_msgs</run_depend>
  <run_depend>shape_msgs</run_depend>
  <run_depend>tf</run_depend>

</package>
