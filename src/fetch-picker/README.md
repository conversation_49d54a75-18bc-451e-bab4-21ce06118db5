# Fetch Picker

This repositiory involves starter code and samples for two classes at the University of Washington, Spring 2022, that aim to program the Fetch mobile manipulator to pick requested items from densely packed shelves.

* [CSE 481 C: Robotics Capstone](https://sites.google.com/cs.washington.edu/cse481csp22/home)
* [TECHIN 517: Robotics Lab II](https://sites.google.com/cs.washington.edu/techin517sp22/home)

Labs and other documentation are on the **[wiki](https://github.com/robotic-picker-sp22/fetch-picker/wiki)**.
