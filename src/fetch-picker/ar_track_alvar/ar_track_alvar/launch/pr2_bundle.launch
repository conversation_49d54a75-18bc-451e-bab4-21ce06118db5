<launch>
	<arg name="marker_size" default="4.4" />
	<arg name="max_new_marker_error" default="0.08" />
	<arg name="max_track_error" default="0.0" />

    <arg name="cam_image_topic" default="/kinect_head/depth_registered/points" />
	<arg name="cam_info_topic" default="/kinect_head/rgb/camera_info" />	

	<arg name="output_frame" default="/torso_lift_link" />
    <arg name="med_filt_size" default="10" />
	<arg name="bundle_files" default="$(find ar_track_alvar)/bundles/truthTableLeg.xml $(find ar_track_alvar)/bundles/table_8_9_10.xml" />

	<node name="ar_track_alvar" pkg="ar_track_alvar" type="findMarkerBundles" respawn="false" output="screen" args="$(arg marker_size) $(arg max_new_marker_error) $(arg max_track_error) $(arg cam_image_topic) $(arg cam_info_topic) $(arg output_frame) $(arg med_filt_size) $(arg bundle_files)" />
</launch>
