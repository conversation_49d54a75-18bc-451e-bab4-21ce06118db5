<launch>
	<arg name="nof_markers" default="8" />
	<arg name="marker_size" default="4.4" />
	<arg name="max_new_marker_error" default="0.08" />
	<arg name="max_track_error" default="0.2" />
	<arg name="cam_image_topic" default="/wide_stereo/left/image_color" />
	<arg name="cam_info_topic" default="/wide_stereo/left/camera_info" />	
	<arg name="output_frame" default="/torso_lift_link" />

	<node name="ar_track_alvar" pkg="ar_track_alvar" type="trainMarkerBundle" respawn="false" output="screen" args="$(arg nof_markers) $(arg marker_size) $(arg max_new_marker_error) $(arg max_track_error) $(arg cam_image_topic) $(arg cam_info_topic) $(arg output_frame)" />
</launch>
