/*
 Software License Agreement (BSD License)

 Copyright (c) 2012, <PERSON>
 All rights reserved.

 Redistribution and use in source and binary forms, with or without
 modification, are permitted provided that the following conditions
 are met:

  * Redistributions of source code must retain the above copyright
    notice, this list of conditions and the following disclaimer.
  * Redistributions in binary form must reproduce the above
    copyright notice, this list of conditions and the following
    disclaimer in the documentation and/or other materials provided
    with the distribution.
  * Neither the name of the Willow Garage nor the names of its
    contributors may be used to endorse or promote products derived
    from this software without specific prior written permission.

 THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
 FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
 COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
 INCIDENTAL, SPECIAL, EXEMPLARY, OR CO<PERSON>EQUENTIAL DAMAGES (INCLUDING,
 BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
 LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
 CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN
 ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 POSSIBILITY OF SUCH DAMAGE.

 author: Scott Niekum
*/

#include <std_msgs/Bool.h>
#include "ar_track_alvar/CvTestbed.h"
#include "ar_track_alvar/MarkerDetector.h"
#include "ar_track_alvar/Shared.h"
#include <cv_bridge/cv_bridge.h>
#include <ar_track_alvar_msgs/AlvarMarker.h>
#include <ar_track_alvar_msgs/AlvarMarkers.h>
#include <tf/transform_listener.h>
#include <tf/transform_broadcaster.h>
#include <sensor_msgs/image_encodings.h>
#include <dynamic_reconfigure/server.h>
#include <ar_track_alvar/ParamsConfig.h>

using namespace alvar;
using namespace std;

bool init = true;
Camera* cam;
cv_bridge::CvImagePtr cv_ptr_;
image_transport::Subscriber cam_sub_;
ros::Publisher arMarkerPub_;
ros::Publisher rvizMarkerPub_;
ar_track_alvar_msgs::AlvarMarkers arPoseMarkers_;
visualization_msgs::Marker rvizMarker_;
tf::TransformListener* tf_listener;
tf::TransformBroadcaster* tf_broadcaster;
MarkerDetector<MarkerData> marker_detector;

bool enableSwitched = false;
bool enabled = true;
double max_frequency = 8.0;
double marker_size;
double max_new_marker_error;
double max_track_error;
std::string cam_image_topic;
std::string cam_info_topic;
std::string output_frame;
int marker_resolution = 5;  // default marker resolution
int marker_margin = 2;      // default marker margin

void getCapCallback(const sensor_msgs::ImageConstPtr& image_msg);

void getCapCallback(const sensor_msgs::ImageConstPtr& image_msg)
{
  // If we've already gotten the cam info, then go ahead
  if (cam->getCamInfo_)
  {
    try
    {
      tf::StampedTransform CamToOutput;
      try
      {
        tf_listener->waitForTransform(output_frame, image_msg->header.frame_id,
                                      image_msg->header.stamp,
                                      ros::Duration(1.0));
        tf_listener->lookupTransform(output_frame, image_msg->header.frame_id,
                                     image_msg->header.stamp, CamToOutput);
      }
      catch (tf::TransformException ex)
      {
        ROS_ERROR("%s", ex.what());
      }

      // Convert the image
      cv_ptr_ =
          cv_bridge::toCvCopy(image_msg, sensor_msgs::image_encodings::BGR8);

      // Get the estimated pose of the main markers by using all the markers in
      // each bundle
      marker_detector.Detect(cv_ptr_->image, cam, true, false,
                             max_new_marker_error, max_track_error, CVSEQ,
                             true);
      arPoseMarkers_.markers.clear();
      for (size_t i = 0; i < marker_detector.markers->size(); i++)
      {
        // Get the pose relative to the camera
        int id = (*(marker_detector.markers))[i].GetId();
        Pose p = (*(marker_detector.markers))[i].pose;
        double px = p.translation[0] / 100.0;
        double py = p.translation[1] / 100.0;
        double pz = p.translation[2] / 100.0;

        cv::Mat quat =cv::Mat(4, 1, CV_64F);
        p.GetQuaternion(quat);
        double qx = quat.at<double>(1,0); //p.quaternion[1]; #leaving these for the record, this was a bug in the original repo
        double qy = quat.at<double>(2,0); //p.quaternion[2];
        double qz = quat.at<double>(3,0); //p.quaternion[3];
        double qw = quat.at<double>(0,0); //p.quaternion[0];

        tf::Quaternion rotation(qx, qy, qz, qw);
        tf::Vector3 origin(px, py, pz);
        tf::Transform t(rotation, origin);
        tf::Vector3 markerOrigin(0, 0, 0);
        tf::Transform m(tf::Quaternion::getIdentity(), markerOrigin);
        tf::Transform markerPose = t * m;  // marker pose in the camera frame

        tf::Vector3 z_axis_cam = tf::Transform(rotation, tf::Vector3(0, 0, 0)) *
                                 tf::Vector3(0, 0, 1);
        //                ROS_INFO("%02i Z in cam frame: %f %f %f",id,
        //                z_axis_cam.x(), z_axis_cam.y(), z_axis_cam.z());
        /// as we can't see through markers, this one is false positive
        /// detection
        if (z_axis_cam.z() > 0)
        {
          continue;
        }

        // Publish the transform from the camera to the marker
        std::string markerFrame = "ar_marker_";
        std::stringstream out;
        out << id;
        std::string id_string = out.str();
        markerFrame += id_string;
        tf::StampedTransform camToMarker(t, image_msg->header.stamp,
                                         image_msg->header.frame_id,
                                         markerFrame.c_str());
        tf_broadcaster->sendTransform(camToMarker);

        // Create the rviz visualization messages
        tf::poseTFToMsg(markerPose, rvizMarker_.pose);
        rvizMarker_.header.frame_id = image_msg->header.frame_id;
        rvizMarker_.header.stamp = image_msg->header.stamp;
        rvizMarker_.id = id;

        rvizMarker_.scale.x = 1.0 * marker_size / 100.0;
        rvizMarker_.scale.y = 1.0 * marker_size / 100.0;
        rvizMarker_.scale.z = 0.2 * marker_size / 100.0;
        rvizMarker_.ns = "basic_shapes";
        rvizMarker_.type = visualization_msgs::Marker::CUBE;
        rvizMarker_.action = visualization_msgs::Marker::ADD;
        switch (id)
        {
          case 0:
            rvizMarker_.color.r = 0.0f;
            rvizMarker_.color.g = 0.0f;
            rvizMarker_.color.b = 1.0f;
            rvizMarker_.color.a = 1.0;
            break;
          case 1:
            rvizMarker_.color.r = 1.0f;
            rvizMarker_.color.g = 0.0f;
            rvizMarker_.color.b = 0.0f;
            rvizMarker_.color.a = 1.0;
            break;
          case 2:
            rvizMarker_.color.r = 0.0f;
            rvizMarker_.color.g = 1.0f;
            rvizMarker_.color.b = 0.0f;
            rvizMarker_.color.a = 1.0;
            break;
          case 3:
            rvizMarker_.color.r = 0.0f;
            rvizMarker_.color.g = 0.5f;
            rvizMarker_.color.b = 0.5f;
            rvizMarker_.color.a = 1.0;
            break;
          case 4:
            rvizMarker_.color.r = 0.5f;
            rvizMarker_.color.g = 0.5f;
            rvizMarker_.color.b = 0.0;
            rvizMarker_.color.a = 1.0;
            break;
          default:
            rvizMarker_.color.r = 0.5f;
            rvizMarker_.color.g = 0.0f;
            rvizMarker_.color.b = 0.5f;
            rvizMarker_.color.a = 1.0;
            break;
        }
        rvizMarker_.lifetime = ros::Duration(1.0);
        rvizMarkerPub_.publish(rvizMarker_);

        // Get the pose of the tag in the camera frame, then the output frame
        // (usually torso)
        tf::Transform tagPoseOutput = CamToOutput * markerPose;

        // Create the pose marker messages
        ar_track_alvar_msgs::AlvarMarker ar_pose_marker;
        tf::poseTFToMsg(tagPoseOutput, ar_pose_marker.pose.pose);
        ar_pose_marker.header.frame_id = output_frame;
        ar_pose_marker.header.stamp = image_msg->header.stamp;
        ar_pose_marker.id = id;
        arPoseMarkers_.markers.push_back(ar_pose_marker);
      }
      arPoseMarkers_.header.stamp = image_msg->header.stamp;
      arPoseMarkers_.header.frame_id = output_frame;
      arMarkerPub_.publish(arPoseMarkers_);
    }
    catch (const std::exception& e)
    {
      ROS_ERROR("Error in ar_track_alvar callback: %s", e.what());
    }
  }
}

void configCallback(ar_track_alvar::ParamsConfig& config, uint32_t level)
{
  ROS_INFO("AR tracker reconfigured: %s %.2f %.2f %.2f %.2f",
           config.enabled ? "ENABLED" : "DISABLED", config.max_frequency,
           config.marker_size, config.max_new_marker_error,
           config.max_track_error);

  enableSwitched = enabled != config.enabled;

  enabled = config.enabled;
  max_frequency = config.max_frequency;
  marker_size = config.marker_size;
  max_new_marker_error = config.max_new_marker_error;
  max_track_error = config.max_track_error;
}

void enableCallback(const std_msgs::BoolConstPtr& msg)
{
  enableSwitched = enabled != msg->data;
  enabled = msg->data;
}

int main(int argc, char* argv[])
{
  ros::init(argc, argv, "marker_detect");
  ros::NodeHandle n, pn("~");

  if (argc > 1)
  {
    ROS_WARN("Command line arguments are deprecated. Consider using ROS "
             "parameters and remappings.");

    if (argc < 7)
    {
      std::cout << std::endl;
      cout << "Not enough arguments provided." << endl;
      cout << "Usage: ./individualMarkersNoKinect <marker size in cm> <max new "
              "marker error> <max track error> "
           << "<cam image topic> <cam info topic> <output frame> [ <max "
              "frequency> <marker_resolution> <marker_margin>]";
      std::cout << std::endl;
      return 0;
    }

    // Get params from command line
    marker_size = atof(argv[1]);
    max_new_marker_error = atof(argv[2]);
    max_track_error = atof(argv[3]);
    cam_image_topic = argv[4];
    cam_info_topic = argv[5];
    output_frame = argv[6];

    if (argc > 7)
    {
      max_frequency = atof(argv[7]);
      pn.setParam("max_frequency", max_frequency);
    }
    if (argc > 8)
      marker_resolution = atoi(argv[8]);
    if (argc > 9)
      marker_margin = atoi(argv[9]);
  }
  else
  {
    // Get params from ros param server.
    pn.param("marker_size", marker_size, 10.0);
    pn.param("max_new_marker_error", max_new_marker_error, 0.08);
    pn.param("max_track_error", max_track_error, 0.2);
    pn.param("max_frequency", max_frequency, 8.0);
    pn.setParam("max_frequency", max_frequency);  // in case it was not set.
    pn.param("marker_resolution", marker_resolution, 5);
    pn.param("marker_margin", marker_margin, 2);
    if (!pn.getParam("output_frame", output_frame))
    {
      ROS_ERROR("Param 'output_frame' has to be set.");
      exit(EXIT_FAILURE);
    }

    // Camera input topics. Use remapping to map to your camera topics.
    cam_image_topic = "camera_image";
    cam_info_topic = "camera_info";
  }

  // Set dynamically configurable parameters so they don't get replaced by
  // default values
  pn.setParam("marker_size", marker_size);
  pn.setParam("max_new_marker_error", max_new_marker_error);
  pn.setParam("max_track_error", max_track_error);

  marker_detector.SetMarkerSize(marker_size, marker_resolution, marker_margin);

  cam = new Camera(n, cam_info_topic);
  tf_listener = new tf::TransformListener(n);
  tf_broadcaster = new tf::TransformBroadcaster();
  arMarkerPub_ =
      n.advertise<ar_track_alvar_msgs::AlvarMarkers>("ar_pose_marker", 0);
  rvizMarkerPub_ =
      n.advertise<visualization_msgs::Marker>("visualization_marker", 0);

  // Prepare dynamic reconfiguration
  dynamic_reconfigure::Server<ar_track_alvar::ParamsConfig> server;
  dynamic_reconfigure::Server<ar_track_alvar::ParamsConfig>::CallbackType f;

  f = boost::bind(&configCallback, _1, _2);
  server.setCallback(f);

  // Give tf a chance to catch up before the camera callback starts asking for
  // transforms
  // It will also reconfigure parameters for the first time, setting the default
  // values
  ros::Duration(1.0).sleep();
  ros::spinOnce();

  image_transport::ImageTransport it_(n);

  // Run at the configured rate, discarding pointcloud msgs if necessary
  ros::Rate rate(max_frequency);

  /// Subscriber for enable-topic so that a user can turn off the detection if
  /// it is not used without having to use the reconfigure where he has to know
  /// all parameters
  ros::Subscriber enable_sub_ =
      pn.subscribe("enable_detection", 1, &enableCallback);

  enableSwitched = true;
  while (ros::ok())
  {
    ros::spinOnce();
    rate.sleep();

    if (std::abs((rate.expectedCycleTime() - ros::Duration(1.0 / max_frequency))
                     .toSec()) > 0.001)
    {
      // Change rate dynamically; if must be above 0, as 0 will provoke a
      // segfault on next spinOnce
      ROS_DEBUG("Changing frequency from %.2f to %.2f",
                1.0 / rate.expectedCycleTime().toSec(), max_frequency);
      rate = ros::Rate(max_frequency);
    }

    if (enableSwitched)
    {
      // Enable/disable switch: subscribe/unsubscribe to make use of pointcloud
      // processing nodelet lazy publishing policy; in CPU-scarce computer as
      // TurtleBot's laptop this is a huge saving
      if (enabled)
        cam_sub_ = it_.subscribe(cam_image_topic, 1, &getCapCallback);
      else
        cam_sub_.shutdown();
      enableSwitched = false;
    }
  }

  return 0;
}
