#!/usr/bin/env python

PACKAGE = "ar_track_alvar"

from dynamic_reconfigure.parameter_generator_catkin import *

gen = ParameterGenerator()

gen.add("enabled", bool_t, 0, "Enable/disable tracking, unsubscribing from camera topic to stop openni processing", True)
gen.add("max_frequency", double_t, 0, "Maximum processing rate; frames coming at a higher rate are discarded", 10.0, 1.0, 30.0)
gen.add("marker_size", double_t, 0, "The width in centimeters of one side of the black square marker border", 10.0, 1.0, 100.0)
gen.add("max_new_marker_error", double_t, 0, "A threshold determining when new markers can be detected under uncertainty", 0.08, 0.0, 2.0)
gen.add("max_track_error", double_t, 0, "A threshold determining how much tracking error can be observed before an tag is considered to have disappeared", 0.2, 0.0, 4.0)

# Second arg is node name it will run in (doc purposes only), third is generated filename prefix
exit(gen.generate(PACKAGE, "ar_track_alvar_configure", "Params"))
