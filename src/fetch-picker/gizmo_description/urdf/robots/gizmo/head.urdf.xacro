<?xml version="1.0" ?>
<!-- this is the -->
<robot name="head" xmlns:xacro="http://ros.org/wiki/xacro">
  <xacro:include filename="$(find gizmo_description)/urdf/robots/gizmo/simple.transmission.xacro"/>

  <!-- the head contains the neck joints and the eye lids -->
  <xacro:macro name="head">

    <simple_transmission name="head_1" />
    <simple_transmission name="head_2" />
    <simple_transmission name="eyelids" />

    <joint name="head_1_joint" type="revolute">
      <origin xyz="0.0 0.0 0.360" rpy="0 0 0" />
      <parent link="body_link"/>
      <child link="head_1_link" />
      <axis xyz="0 0 1"/>
      <limit lower="-1.0" upper="1.0" effort="20.0" velocity="2.0" />
    </joint>

    <link name="head_1_link">
      <inertial>
        <!-- COM -->
        <origin xyz="0.0 0 0"/>
        <mass value="0.1"/>
        <inertia ixx="0.001" ixy="0.0" ixz="0.0"
                 iyy="0.001" iyz="0.0"
                 izz="0.001" />
      </inertial>
    </link>

    <joint name="head_2_joint" type="revolute">
      <origin xyz="0.0 0.0 0.0" rpy="0 0 0" />
      <parent link="head_1_link"/>
      <child link="head_2_link" />
      <axis xyz="0 1 0"/>
      <limit lower="-0.52" upper="0.87" effort="20.0" velocity="2.0" />
    </joint>

    <link name="head_2_link">
      <visual>
        <geometry>
          <mesh filename="package://gizmo_description/meshes/gizmo/head.dae" />
        </geometry>
        <material name="DarkGrey" />
      </visual>
      <collision>
        <geometry>
          <sphere radius="0.1" />
        </geometry>
      </collision>
      <inertial>
        <!-- COM -->
        <origin xyz="0.0 0 0"/>
        <mass value="1.0"/>
        <inertia ixx="0.01" ixy="0.0" ixz="0.0"
                 iyy="0.01" iyz="0.0"
                 izz="0.01" />
      </inertial>
    </link>

    <joint name="eyelids_joint" type="revolute">
      <origin xyz="0.0 0.0 0.0" rpy="0 0 0" />
      <parent link="head_2_link"/>
      <child link="eyelids_link" />
      <axis xyz="0 1 0"/>
      <limit lower="-1.0" upper="1.0" effort="20.0" velocity="10.0" />
    </joint>

    <link name="eyelids_link">
      <visual>
        <geometry>
          <mesh filename="package://gizmo_description/meshes/gizmo/eyelids.dae" />
        </geometry>
      </visual>
      <inertial>
        <!-- COM -->
        <origin xyz="0.0 0 0"/>
        <mass value="0.1"/>
        <inertia ixx="0.001" ixy="0.0" ixz="0.0"
                 iyy="0.001" iyz="0.0"
                 izz="0.001" />
      </inertial>
    </link>

  </xacro:macro>
</robot>
