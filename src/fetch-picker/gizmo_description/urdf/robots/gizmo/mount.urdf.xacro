<?xml version='1.0'?>

<robot name="mount" xmlns:xacro="http://ros.org/wiki/xacro">
  <xacro:macro name="mount" params="parent">
    <joint name="laptop_mount_joint" type="fixed">
      <origin xyz="${mount_px} ${mount_py} ${mount_pz}" rpy="${mount_or} ${mount_op} ${mount_oy}"/>
      <parent link="${parent}"/>
      <child link="laptop_mount_link"/>
    </joint>  
    
    <link name="laptop_mount_link">
      <visual>
        <origin xyz="0.0 0.0 0.122" rpy="0 0 0" />
        <geometry>
          <cylinder length="0.23" radius="0.015"/>
          <!--mesh filename="package://gizmo_description/meshes/stacks/laptop_mount/mount_link.STL" scale="0.001 0.001 0.001"/-->
        </geometry>
        <material name="">
          <color rgba="0.1 0.1 0.1 1"/>
        </material>
      </visual>
      <inertial>
        <mass value="0.001" />
        <origin xyz="0 0 0" />
        <inertia ixx="0.0001" ixy="0.0" ixz="0.0"
           iyy="0.0001" iyz="0.0"
           izz="0.0001" />
      </inertial>
    </link>
  </xacro:macro>
</robot>
