<?xml version="1.0"?>
<robot name="turtlebot_gazebo" xmlns:xacro="http://ros.org/wiki/xacro">
  <!-- Microsoft Kinect / ASUS Xtion PRO Live for simulation -->
  <xacro:macro name="turtlebot_sim_3dsensor">
    <gazebo reference="asus_link">  
      <sensor type="depth" name="camera">
        <always_on>true</always_on>
        <update_rate>20.0</update_rate>
        <camera>
          <horizontal_fov>${60.0*M_PI/180.0}</horizontal_fov>
          <image>
            <format>R8G8B8</format>
            <width>640</width>
            <height>480</height>
          </image>
          <clip>
            <near>0.05</near>
            <far>8.0</far>
          </clip>
        </camera>
        <plugin name="kinect_camera_controller" filename="libgazebo_ros_openni_kinect.so">
          <cameraName>asus</cameraName>
          <alwaysOn>true</alwaysOn>
          <updateRate>10</updateRate>
          <imageTopicName>rgb/image_raw</imageTopicName>
          <depthImageTopicName>depth/image_raw</depthImageTopicName>
          <pointCloudTopicName>depth/points</pointCloudTopicName>
          <cameraInfoTopicName>rgb/camera_info</cameraInfoTopicName>
          <depthImageCameraInfoTopicName>depth/camera_info</depthImageCameraInfoTopicName>
          <frameName>asus_depth_optical_frame</frameName>
          <baseline>0.1</baseline>
          <distortion_k1>0.0</distortion_k1>
          <distortion_k2>0.0</distortion_k2>
          <distortion_k3>0.0</distortion_k3>
          <distortion_t1>0.0</distortion_t1>
          <distortion_t2>0.0</distortion_t2>
          <pointCloudCutoff>0.4</pointCloudCutoff>
        </plugin>
      </sensor>
    </gazebo>
  </xacro:macro>

  <xacro:macro name="turtlebot_sim_hokuyo" params="suffix">
    <gazebo reference="hokuyo_laser_link${suffix}">
      <sensor type="ray" name="head_hokuyo_sensor${suffix}">
	<pose>0 0 0 0 0 0</pose>
	<visualize>false</visualize>
	<update_rate>10</update_rate>
	<ray>
	  <scan>
	    <horizontal>
	      <samples>540</samples>
	      <resolution>1</resolution>
	      <min_angle>-1.5708</min_angle>
	      <max_angle>1.5708</max_angle>
	    </horizontal>
	  </scan>
	  <range>
	    <min>0.1</min>
	    <max>15.0</max>
	    <resolution>0.01</resolution>
	  </range>
	  <noise>
	    <type>gaussian</type>
	    <!-- Noise parameters based on published spec for Hokuyo laser
		 achieving "+-30mm" accuracy at range < 10m.  A mean of 0.0m and
		 stddev of 0.01m will put 99.7% of samples within 0.03m of the true
		 reading. -->
	    <mean>0.0</mean>
	    <stddev>0.01</stddev>
	  </noise>
	</ray>
	<plugin name="gazebo_ros_head_hokuyo_controller" filename="libgazebo_ros_laser.so">
	  <topicName>/scan${suffix}</topicName>
	  <frameName>hokuyo_laser_link${suffix}</frameName>
	</plugin>
      </sensor>
    </gazebo>
  </xacro:macro>

  <xacro:macro name="turtlebot_sim_sonar" params="name">
    <gazebo reference="range_sensor_${name}_link">
      <sensor type="ray" name="${name}">
        <always_on>true</always_on>
        <update_rate>3</update_rate>
        <pose>0 0 0 0 0 0</pose>
        <visualize>false</visualize>
        <ray>
          <scan>
            <horizontal>
              <samples>20</samples>
              <resolution>1</resolution>
              <min_angle>-0.44</min_angle>
              <max_angle> 0.44</max_angle>
            </horizontal>
            <vertical>
              <samples>10</samples>
              <resolution>1</resolution>
              <min_angle>-0.26</min_angle>
              <max_angle> 0.26</max_angle>
            </vertical>
          </scan>
          <range>
            <min>0.05</min>
            <max>3.0</max>
            <resolution>0.01</resolution>
          </range>
        </ray>
	
        <plugin name="gazebo_ros_${name}_controller" filename="libhector_gazebo_ros_sonar.so">
          <gaussianNoise>0.005</gaussianNoise>
          <topicName>sonars</topicName>
          <frameId>range_sensor_${name}_link</frameId>
          <updateRate>3.0</updateRate>
        </plugin>
      </sensor>
    </gazebo>
  </xacro:macro>

  <xacro:macro name="turtlebot_sim_usb_camera" params="name">
    <gazebo reference="${name}_camera_link">
      <sensor type="camera" name="${name}_sensor">
	<update_rate>15.0</update_rate>
	<camera name="${name}_camera">
	  <horizontal_fov>1.13</horizontal_fov>
	  <image>
	    <width>640</width>
	    <height>480</height>
	    <format>R8G8B8</format>
	  </image>
	  <clip>
	    <near>0.02</near>
	    <far>300</far>
	  </clip>
	  <noise>
	    <type>gaussian</type>
	    <!-- Noise is sampled independently per pixel on each frame.  
		 That pixel's noise value is added to each of its color
		 channels, which at that point lie in the range [0,1]. -->
	    <mean>0.0</mean>
	    <stddev>0.007</stddev>
	  </noise>
	</camera>
	<plugin name="camera_controller" filename="libgazebo_ros_camera.so">
	  <alwaysOn>true</alwaysOn>
	  <updateRate>15.0</updateRate>
	  <cameraName>${name}_camera</cameraName>
	  <imageTopicName>image_raw</imageTopicName>
	  <cameraInfoTopicName>camera_info</cameraInfoTopicName>
	  <frameName>${name}_camera_link</frameName>
	  <hackBaseline>0.0</hackBaseline>
	  <distortionK1>0.0</distortionK1>
	  <distortionK2>0.0</distortionK2>
	  <distortionK3>0.0</distortionK3>
	  <distortionT1>0.0</distortionT1>
	  <distortionT2>0.0</distortionT2>
	</plugin>
      </sensor>
    </gazebo>  
  </xacro:macro>
</robot>
