<?xml version="1.0"?>
<robot name="pico_flexx_robot" xmlns:xacro="http://ros.org/wiki/xacro">

  <xacro:macro name="pico_flexx" params="parent px py pz or op oy">
    <joint name="pico_flexx" type="fixed">
      <origin xyz="${px} ${py} ${pz}" rpy="${or} ${op} ${oy}" />
      <parent link="${parent}" />
      <child link="pico_flexx_optical_frame"/>
    </joint>  
    
    <link name="pico_flexx_optical_frame">
      <visual>
       <origin xyz="0 0 0" rpy="0 0 0"/>
        <geometry>
         <mesh filename="package://gizmo_description/meshes/sensors/kinect.dae"/>
        </geometry>
      </visual>
        <collision>
        <origin xyz="0.0 0.0 0.0" rpy="0 0 0"/>
          <geometry>
            <box size="0.07271 0.27794 0.073"/>
          </geometry>
        </collision>
      <inertial>
        <mass value="0.001" />
        <origin xyz="0 0 0" />
        <inertia ixx="0.0001" ixy="0.0" ixz="0.0"
                 iyy="0.0001" iyz="0.0"
                 izz="0.0001" />
      </inertial>
    </link>
  </xacro:macro>

</robot>
