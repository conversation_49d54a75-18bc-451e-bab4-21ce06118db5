<?xml version="1.0" encoding="UTF-8"?>
<launch>

  <arg name="world_name" default="$(find fetchit_challenge)/worlds/fetchit_challenge_arena_montreal2019.world"/>
  <arg name="paused" default="false"/>
  <arg name="verbose" default="true"/>
  <arg name="physics" default="ode"/>

  <include file="$(find gazebo_ros)/launch/empty_world.launch">
    <arg name="world_name" value="$(arg world_name)"/>
    <arg name="paused" value="$(arg paused)"/>
    <arg name="verbose" value="$(arg verbose)"/>
    <arg name="physics" default="$(arg physics)"/>
  </include>
</launch>
