<?xml version="1.0" ?>
<sdf version='1.4'>
  <model name="gearbox_bolt_blue">
      <static>0</static>
      <link name='link'>
        <inertial>
          <pose>0 0 0.030633 0 0 0</pose>
          <mass>0.0592855</mass>
          <inertia>
            <ixx>2.2977502034e-05</ixx>
            <ixy>0.000000</ixy>
            <ixz>0.000000</ixz>
            <iyy>2.29905352792e-05</iyy>
            <iyz>0.000000</iyz>
            <izz>8.87979175473e-06</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://gearbox_bolt_blue/meshes/gear_bolt_simple.stl</uri>
              <scale>1 1 1</scale>
            </mesh>
          </geometry>
          <surface>
            <friction>
              <ode>
                <mu>30.0</mu>
                <mu2>30.0</mu2>
              </ode>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://gearbox_bolt_blue/meshes/screw_blue.dae</uri>
              <scale>1 1 1</scale>
            </mesh>
          </geometry>
        </visual>
        <velocity_decay>
          <linear>0.000000</linear>
          <angular>0.000000</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>        
      </link>   
  </model>
</sdf>
