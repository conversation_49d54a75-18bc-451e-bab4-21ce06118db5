<?xml version="1.0" encoding="utf-8"?>
<COLLADA xmlns="http://www.collada.org/2005/11/COLLADASchema" version="1.4.1" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <asset>
    <contributor>
      <author>Blender User</author>
      <authoring_tool>Blender 2.79.0 commit date:2018-03-22, commit time:14:10, hash:f4dc9f9d68b</authoring_tool>
    </contributor>
    <created>2019-02-27T11:16:34</created>
    <modified>2019-02-27T11:16:34</modified>
    <unit name="meter" meter="1"/>
    <up_axis>Z_UP</up_axis>
  </asset>
  <library_images>
    <image id="FetchLogo-1024x388_png_001" name="FetchLogo-1024x388_png_001">
      <init_from>FetchLogo-1024x388.png.001.png</init_from>
    </image>
    <image id="white_png" name="white_png">
      <init_from>white.png</init_from>
    </image>
  </library_images>
  <library_effects>
    <effect id="white_png-effect">
      <profile_COMMON>
        <newparam sid="white_png-surface">
          <surface type="2D">
            <init_from>white_png</init_from>
          </surface>
        </newparam>
        <newparam sid="white_png-sampler">
          <sampler2D>
            <source>white_png-surface</source>
          </sampler2D>
        </newparam>
        <technique sid="common">
          <phong>
            <diffuse>
              <texture texture="white_png-sampler" texcoord="white_png"/>
            </diffuse>
            <specular>
              <color sid="specular">0 0 0 1</color>
            </specular>
          </phong>
        </technique>
      </profile_COMMON>
    </effect>
    <effect id="FetchLogo-1024x388_png_001-effect">
      <profile_COMMON>
        <newparam sid="FetchLogo-1024x388_png_001-surface">
          <surface type="2D">
            <init_from>FetchLogo-1024x388_png_001</init_from>
          </surface>
        </newparam>
        <newparam sid="FetchLogo-1024x388_png_001-sampler">
          <sampler2D>
            <source>FetchLogo-1024x388_png_001-surface</source>
          </sampler2D>
        </newparam>
        <technique sid="common">
          <phong>
            <diffuse>
              <texture texture="FetchLogo-1024x388_png_001-sampler" texcoord="FetchLogo-1024x388_png_001"/>
            </diffuse>
            <specular>
              <color sid="specular">0 0 0 1</color>
            </specular>
          </phong>
        </technique>
      </profile_COMMON>
    </effect>
  </library_effects>
  <library_materials>
    <material id="white_png-material" name="white_png">
      <instance_effect url="#white_png-effect"/>
    </material>
    <material id="FetchLogo-1024x388_png_001-material" name="FetchLogo-1024x388_png_001">
      <instance_effect url="#FetchLogo-1024x388_png_001-effect"/>
    </material>
  </library_materials>
  <library_geometries>
    <geometry id="Cube_001-mesh" name="Cube.001">
      <mesh>
        <source id="Cube_001-mesh-positions">
          <float_array id="Cube_001-mesh-positions-array" count="24">-0.4625 -0.235 0 -0.4625 -0.235 0.8 -0.4625 0.235 0 -0.4625 0.235 0.8 0.4625 -0.235 0 0.4625 -0.235 0.8 0.4625 0.235 0 0.4625 0.235 0.8</float_array>
          <technique_common>
            <accessor source="#Cube_001-mesh-positions-array" count="8" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="Cube_001-mesh-normals">
          <float_array id="Cube_001-mesh-normals-array" count="18">-1 0 0 0 1 0 1 0 0 0 -1 0 0 0 -1 0 0 1</float_array>
          <technique_common>
            <accessor source="#Cube_001-mesh-normals-array" count="6" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="Cube_001-mesh-map-0">
          <float_array id="Cube_001-mesh-map-0-array" count="72">0.0802583 0.8300318 0.02423268 0.7346687 0.0802583 0.7346688 0.09861314 0.8187575 0.01442128 0.745943 0.09861314 0.745943 0.07372623 0.8295714 0.02304852 0.7433114 0.07372623 0.7433114 1.001982 0.9363073 -0.006851792 0.06380206 1.001982 0.063802 0.1050401 0.7539781 0.003378391 0.8056333 0.003378391 0.7539781 7.90274e-5 0.6236364 0.3315678 0.4552042 0.3315678 0.6236364 0.0802583 0.8300318 0.02423268 0.8300317 0.02423268 0.7346687 0.09861314 0.8187575 0.01442128 0.8187575 0.01442128 0.745943 0.07372623 0.8295714 0.02304846 0.8295714 0.02304852 0.7433114 1.001982 0.9363073 -0.006851613 0.9363074 -0.006851792 0.06380206 0.1050401 0.7539781 0.1050401 0.8056333 0.003378391 0.8056333 7.90274e-5 0.6236364 7.89934e-5 0.4552043 0.3315678 0.4552042</float_array>
          <technique_common>
            <accessor source="#Cube_001-mesh-map-0-array" count="36" stride="2">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <vertices id="Cube_001-mesh-vertices">
          <input semantic="POSITION" source="#Cube_001-mesh-positions"/>
        </vertices>
        <triangles material="white_png-material" count="2">
          <input semantic="VERTEX" source="#Cube_001-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#Cube_001-mesh-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#Cube_001-mesh-map-0" offset="2" set="0"/>
          <p>3 5 15 5 5 16 7 5 17 3 5 33 1 5 34 5 5 35</p>
        </triangles>
        <triangles material="FetchLogo-1024x388_png_001-material" count="10">
          <input semantic="VERTEX" source="#Cube_001-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#Cube_001-mesh-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#Cube_001-mesh-map-0" offset="2" set="0"/>
          <p>1 0 0 2 0 1 0 0 2 3 1 3 6 1 4 2 1 5 7 2 6 4 2 7 6 2 8 5 3 9 0 3 10 4 3 11 6 4 12 0 4 13 2 4 14 1 0 18 3 0 19 2 0 20 3 1 21 7 1 22 6 1 23 7 2 24 5 2 25 4 2 26 5 3 27 1 3 28 0 3 29 6 4 30 4 4 31 0 4 32</p>
        </triangles>
      </mesh>
    </geometry>
  </library_geometries>
  <library_controllers/>
  <library_visual_scenes>
    <visual_scene id="Scene" name="Scene">
      <node id="Cube" name="Cube" type="NODE">
        <matrix sid="transform">1 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry url="#Cube_001-mesh" name="Cube">
          <bind_material>
            <technique_common>
              <instance_material symbol="white_png-material" target="#white_png-material">
                <bind_vertex_input semantic="UVMap" input_semantic="TEXCOORD" input_set="0"/>
              </instance_material>
              <instance_material symbol="FetchLogo-1024x388_png_001-material" target="#FetchLogo-1024x388_png_001-material">
                <bind_vertex_input semantic="UVMap" input_semantic="TEXCOORD" input_set="0"/>
              </instance_material>
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
    </visual_scene>
  </library_visual_scenes>
  <scene>
    <instance_visual_scene url="#Scene"/>
  </scene>
</COLLADA>