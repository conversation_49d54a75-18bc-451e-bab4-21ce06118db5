<?xml version="1.0" ?>
<sdf version='1.4'>
  <model name="fetchit_table_sick">
      <static>1</static>
      <link name='link'>
        <inertial>
          <mass>0.0592855</mass>
          <inertia>
            <ixx>2.31818050784e-05</ixx>
            <ixy>0.000000</ixy>
            <ixz>0.000000</ixz>
            <iyy>2.31851837098e-05</iyy>
            <iyz>0.000000</iyz>
            <izz>8.88944636856e-06</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://fetchit_table_sick/meshes/simple_table.stl</uri>
              <scale>1 1 1</scale>
            </mesh>
          </geometry>
          <surface>
            <friction>
              <ode>
                <mu>30.0</mu>
                <mu2>30.0</mu2>
              </ode>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://fetchit_table_sick/meshes/table_sick.dae</uri>
              <scale>1 1 1</scale>
            </mesh>
          </geometry>
        </visual>
        <velocity_decay>
          <linear>0.000000</linear>
          <angular>0.000000</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>        
      </link>   
  </model>
</sdf>
