<?xml version="1.0" ?>
<sdf version='1.4'>
  <model name="fetchit_simple_env">
      <static>1</static>
      <link name='link'>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://fetchit_arena/meshes/fetchit_arena1.dae</uri>
              <scale>1 1 1</scale>
            </mesh>
          </geometry>
        </visual>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://fetchit_arena/meshes/fetchit_arena1.dae</uri>
              <scale>1 1 1</scale>
            </mesh>
          </geometry>
          <surface>
            <friction>
              <ode>
                <mu>30.0</mu>
                <mu2>30.0</mu2>
              </ode>
            </friction>
            <contact>
              <ode>
                <soft_cfm>0</soft_cfm>
    	          <soft_erp>0.2</soft_erp>
    	          <kp>1e+13</kp>
    	          <kd>1</kd>
    	          <max_vel>0.01</max_vel>
    	          <min_depth>0</min_depth>
              </ode>
            </contact>
          </surface>
        </collision>
        <velocity_decay>
          <linear>0.000000</linear>
          <angular>0.000000</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>        
      </link>   
  </model>
</sdf>
