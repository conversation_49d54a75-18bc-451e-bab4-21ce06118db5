<?xml version="1.0" ?>
<sdf version='1.4'>
  <model name="100mmbin">
      <static>1</static>
      <link name='link'>
        <inertial>
          <mass>0.0189132</mass>
          <inertia>
            <ixx>3.36060179959e-06</ixx>
            <ixy>0.000000</ixy>
            <ixz>0.000000</ixz>
            <iyy>3.36060179959e-06</iyy>
            <iyz>0.000000</iyz>
            <izz>1.34975977923e-06</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://100mmbin/meshes/100mm.stl</uri>
              <scale>1 1 1</scale>
            </mesh>
          </geometry>
          <surface>
            <friction>
              <ode>
                <mu>30.0</mu>
                <mu2>30.0</mu2>
              </ode>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://100mmbin/meshes/100mm.dae</uri>
              <scale>1 1 1</scale>
            </mesh>
          </geometry>
        </visual>
        <velocity_decay>
          <linear>0.000000</linear>
          <angular>0.000000</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>        
      </link>   
  </model>
</sdf>
