<?xml version="1.0" encoding="utf-8"?>
<COLLADA xmlns="http://www.collada.org/2005/11/COLLADASchema" version="1.4.1" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <asset>
    <contributor>
      <author>Blender User</author>
      <authoring_tool>Blender 2.79.0 commit date:2018-03-22, commit time:14:10, hash:f4dc9f9d68b</authoring_tool>
    </contributor>
    <created>2019-04-12T11:58:30</created>
    <modified>2019-04-12T11:58:30</modified>
    <unit name="meter" meter="1"/>
    <up_axis>Z_UP</up_axis>
  </asset>
  <library_images>
    <image id="fetch_logo_white_background_png" name="fetch_logo_white_background_png">
      <init_from>fetch_logo_white_background.png</init_from>
    </image>
  </library_images>
  <library_effects>
    <effect id="table_base-effect">
      <profile_COMMON>
        <technique sid="common">
          <phong>
            <emission>
              <color sid="emission">0 0 0 1</color>
            </emission>
            <ambient>
              <color sid="ambient">0 0 0 1</color>
            </ambient>
            <diffuse>
              <color sid="diffuse">1 1 1 1</color>
            </diffuse>
            <specular>
              <color sid="specular">1 1 1 1</color>
            </specular>
            <shininess>
              <float sid="shininess">1</float>
            </shininess>
            <index_of_refraction>
              <float sid="index_of_refraction">1</float>
            </index_of_refraction>
          </phong>
        </technique>
      </profile_COMMON>
    </effect>
    <effect id="fetch_logo_white_background-effect">
      <profile_COMMON>
        <newparam sid="fetch_logo_white_background_png-surface">
          <surface type="2D">
            <init_from>fetch_logo_white_background_png</init_from>
          </surface>
        </newparam>
        <newparam sid="fetch_logo_white_background_png-sampler">
          <sampler2D>
            <source>fetch_logo_white_background_png-surface</source>
          </sampler2D>
        </newparam>
        <technique sid="common">
          <lambert>
            <emission>
              <color sid="emission">0 0 0 1</color>
            </emission>
            <ambient>
              <color sid="ambient">0 0 0 1</color>
            </ambient>
            <diffuse>
              <texture texture="fetch_logo_white_background_png-sampler"/>
            </diffuse>
            <transparent opaque="A_ONE">
              <texture texture="fetch_logo_white_background_png-sampler"/>
            </transparent>
            <index_of_refraction>
              <float sid="index_of_refraction">1</float>
            </index_of_refraction>
          </lambert>
        </technique>
      </profile_COMMON>
    </effect>
  </library_effects>
  <library_materials>
    <material id="table_base-material" name="table_base">
      <instance_effect url="#table_base-effect"/>
    </material>
    <material id="fetch_logo_white_background-material" name="fetch_logo_white_background">
      <instance_effect url="#fetch_logo_white_background-effect"/>
    </material>
  </library_materials>
  <library_geometries>
    <geometry id="Cube_001-mesh" name="Cube.001">
      <mesh>
        <source id="Cube_001-mesh-positions">
          <float_array id="Cube_001-mesh-positions-array" count="36">-0.46 -0.23 0 -0.46 -0.23 0.785 -0.46 0.23 0 -0.46 0.23 0.785 0.46 -0.23 0 0.46 -0.23 0.785 0.46 0.23 0 0.46 0.23 0.785 -0.46 -0.231 0 0.46 -0.231 0 -0.46 -0.231 0.785 0.46 -0.231 0.785</float_array>
          <technique_common>
            <accessor source="#Cube_001-mesh-positions-array" count="12" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="Cube_001-mesh-normals">
          <float_array id="Cube_001-mesh-normals-array" count="21">-1 0 0 0 1 0 1 0 0 0 -1 0 0 0 -1 0 0 1 0 -1 0</float_array>
          <technique_common>
            <accessor source="#Cube_001-mesh-normals-array" count="7" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="Cube_001-mesh-map-0">
          <float_array id="Cube_001-mesh-map-0-array" count="84">0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 1 1 0 1</float_array>
          <technique_common>
            <accessor source="#Cube_001-mesh-map-0-array" count="42" stride="2">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <vertices id="Cube_001-mesh-vertices">
          <input semantic="POSITION" source="#Cube_001-mesh-positions"/>
        </vertices>
        <triangles material="table_base-material" count="12">
          <input semantic="VERTEX" source="#Cube_001-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#Cube_001-mesh-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#Cube_001-mesh-map-0" offset="2" set="0"/>
          <p>1 0 0 2 0 1 0 0 2 3 1 3 6 1 4 2 1 5 7 2 6 4 2 7 6 2 8 5 3 9 0 3 10 4 3 11 6 4 12 0 4 13 2 4 14 3 5 15 5 5 16 7 5 17 1 0 21 3 0 22 2 0 23 3 1 24 7 1 25 6 1 26 7 2 27 5 2 28 4 2 29 5 3 30 1 3 31 0 3 32 6 4 33 4 4 34 0 4 35 3 5 36 1 5 37 5 5 38</p>
        </triangles>
        <triangles material="fetch_logo_white_background-material" count="2">
          <input semantic="VERTEX" source="#Cube_001-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#Cube_001-mesh-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#Cube_001-mesh-map-0" offset="2" set="0"/>
          <p>9 6 18 10 6 19 8 6 20 9 6 39 11 6 40 10 6 41</p>
        </triangles>
      </mesh>
    </geometry>
  </library_geometries>
  <library_controllers/>
  <library_visual_scenes>
    <visual_scene id="Scene" name="Scene">
      <node id="Cube" name="Cube" type="NODE">
        <matrix sid="transform">1 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry url="#Cube_001-mesh" name="Cube">
          <bind_material>
            <technique_common>
              <instance_material symbol="table_base-material" target="#table_base-material"/>
              <instance_material symbol="fetch_logo_white_background-material" target="#fetch_logo_white_background-material"/>
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
    </visual_scene>
  </library_visual_scenes>
  <scene>
    <instance_visual_scene url="#Scene"/>
  </scene>
</COLLADA>