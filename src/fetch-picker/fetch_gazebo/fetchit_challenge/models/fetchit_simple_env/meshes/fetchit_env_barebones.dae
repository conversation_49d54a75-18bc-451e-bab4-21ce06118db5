<?xml version="1.0" encoding="utf-8"?>
<COLLADA xmlns="http://www.collada.org/2005/11/COLLADASchema" version="1.4.1" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <asset>
    <contributor>
      <author>Blender User</author>
      <authoring_tool>Blender 2.79.0 commit date:2018-03-22, commit time:14:10, hash:f4dc9f9d68b</authoring_tool>
    </contributor>
    <created>2019-02-26T21:23:37</created>
    <modified>2019-02-26T21:23:37</modified>
    <unit name="meter" meter="1"/>
    <up_axis>Z_UP</up_axis>
  </asset>
  <library_images>
    <image id="FetchitChallenge_jpg" name="FetchitChallenge_jpg">
      <init_from>FetchitChallenge.jpg</init_from>
    </image>
    <image id="fetchit_manipulation_challenge_logo_png" name="fetchit_manipulation_challenge_logo_png">
      <init_from>fetchit_manipulation_challenge_logo.png</init_from>
    </image>
  </library_images>
  <library_effects>
    <effect id="Floor-effect">
      <profile_COMMON>
        <technique sid="common">
          <phong>
            <emission>
              <color sid="emission">0 0 0 1</color>
            </emission>
            <ambient>
              <color sid="ambient">0 0 0 1</color>
            </ambient>
            <diffuse>
              <color sid="diffuse">0.1712278 0.1712278 0.1712278 1</color>
            </diffuse>
            <specular>
              <color sid="specular">0.07871697 0.07871697 0.07871697 1</color>
            </specular>
            <shininess>
              <float sid="shininess">156</float>
            </shininess>
            <index_of_refraction>
              <float sid="index_of_refraction">1</float>
            </index_of_refraction>
          </phong>
        </technique>
      </profile_COMMON>
    </effect>
    <effect id="SponsorWall-effect">
      <profile_COMMON>
        <technique sid="common">
          <phong>
            <emission>
              <color sid="emission">0 0 0 1</color>
            </emission>
            <ambient>
              <color sid="ambient">0 0 0 1</color>
            </ambient>
            <diffuse>
              <color sid="diffuse">0.64 0.64 0.64 1</color>
            </diffuse>
            <specular>
              <color sid="specular">0.5 0.5 0.5 1</color>
            </specular>
            <shininess>
              <float sid="shininess">50</float>
            </shininess>
            <index_of_refraction>
              <float sid="index_of_refraction">1</float>
            </index_of_refraction>
          </phong>
        </technique>
      </profile_COMMON>
    </effect>
    <effect id="SponsorWallPicture-effect">
      <profile_COMMON>
        <newparam sid="FetchitChallenge_jpg-surface">
          <surface type="2D">
            <init_from>FetchitChallenge_jpg</init_from>
          </surface>
        </newparam>
        <newparam sid="FetchitChallenge_jpg-sampler">
          <sampler2D>
            <source>FetchitChallenge_jpg-surface</source>
          </sampler2D>
        </newparam>
        <technique sid="common">
          <phong>
            <emission>
              <color sid="emission">0 0 0 1</color>
            </emission>
            <ambient>
              <color sid="ambient">0 0 0 1</color>
            </ambient>
            <diffuse>
              <texture texture="FetchitChallenge_jpg-sampler" texcoord="UVMap"/>
            </diffuse>
            <specular>
              <color sid="specular">0.5 0.5 0.5 1</color>
            </specular>
            <shininess>
              <float sid="shininess">50</float>
            </shininess>
            <index_of_refraction>
              <float sid="index_of_refraction">1</float>
            </index_of_refraction>
          </phong>
        </technique>
      </profile_COMMON>
    </effect>
    <effect id="FetchitWall-effect">
      <profile_COMMON>
        <technique sid="common">
          <phong>
            <emission>
              <color sid="emission">0 0 0 1</color>
            </emission>
            <ambient>
              <color sid="ambient">0 0 0 1</color>
            </ambient>
            <diffuse>
              <color sid="diffuse">0.64 0.64 0.64 1</color>
            </diffuse>
            <specular>
              <color sid="specular">0.5 0.5 0.5 1</color>
            </specular>
            <shininess>
              <float sid="shininess">50</float>
            </shininess>
            <index_of_refraction>
              <float sid="index_of_refraction">1</float>
            </index_of_refraction>
          </phong>
        </technique>
      </profile_COMMON>
    </effect>
    <effect id="FetchitWallPicture-effect">
      <profile_COMMON>
        <newparam sid="fetchit_manipulation_challenge_logo_png-surface">
          <surface type="2D">
            <init_from>fetchit_manipulation_challenge_logo_png</init_from>
          </surface>
        </newparam>
        <newparam sid="fetchit_manipulation_challenge_logo_png-sampler">
          <sampler2D>
            <source>fetchit_manipulation_challenge_logo_png-surface</source>
          </sampler2D>
        </newparam>
        <technique sid="common">
          <phong>
            <emission>
              <color sid="emission">0 0 0 1</color>
            </emission>
            <ambient>
              <color sid="ambient">0 0 0 1</color>
            </ambient>
            <diffuse>
              <texture texture="fetchit_manipulation_challenge_logo_png-sampler" texcoord="UVMap"/>
            </diffuse>
            <specular>
              <color sid="specular">0.5 0.5 0.5 1</color>
            </specular>
            <shininess>
              <float sid="shininess">50</float>
            </shininess>
            <index_of_refraction>
              <float sid="index_of_refraction">1</float>
            </index_of_refraction>
          </phong>
        </technique>
      </profile_COMMON>
    </effect>
  </library_effects>
  <library_materials>
    <material id="Floor-material" name="Floor">
      <instance_effect url="#Floor-effect"/>
    </material>
    <material id="SponsorWall-material" name="SponsorWall">
      <instance_effect url="#SponsorWall-effect"/>
    </material>
    <material id="SponsorWallPicture-material" name="SponsorWallPicture">
      <instance_effect url="#SponsorWallPicture-effect"/>
    </material>
    <material id="FetchitWall-material" name="FetchitWall">
      <instance_effect url="#FetchitWall-effect"/>
    </material>
    <material id="FetchitWallPicture-material" name="FetchitWallPicture">
      <instance_effect url="#FetchitWallPicture-effect"/>
    </material>
  </library_materials>
  <library_geometries>
    <geometry id="Plane-mesh" name="Plane">
      <mesh>
        <source id="Plane-mesh-positions">
          <float_array id="Plane-mesh-positions-array" count="12">-1 -1 0 1 -1 0 -1 1 0 1 1 0</float_array>
          <technique_common>
            <accessor source="#Plane-mesh-positions-array" count="4" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="Plane-mesh-normals">
          <float_array id="Plane-mesh-normals-array" count="3">0 0 1</float_array>
          <technique_common>
            <accessor source="#Plane-mesh-normals-array" count="1" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <vertices id="Plane-mesh-vertices">
          <input semantic="POSITION" source="#Plane-mesh-positions"/>
        </vertices>
        <triangles material="Floor-material" count="2">
          <input semantic="VERTEX" source="#Plane-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#Plane-mesh-normals" offset="1"/>
          <p>1 0 2 0 0 0 1 0 3 0 2 0</p>
        </triangles>
      </mesh>
    </geometry>
    <geometry id="Cube_001-mesh" name="Cube.001">
      <mesh>
        <source id="Cube_001-mesh-positions">
          <float_array id="Cube_001-mesh-positions-array" count="24">-1 -1 -1 -1 -1 1 -1 1 -1 -1 1 1 1 -1 -1 1 -1 1 1 1 -1 1 1 1</float_array>
          <technique_common>
            <accessor source="#Cube_001-mesh-positions-array" count="8" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="Cube_001-mesh-normals">
          <float_array id="Cube_001-mesh-normals-array" count="18">-1 0 0 0 1 0 1 0 0 0 -1 0 0 0 -1 0 0 1</float_array>
          <technique_common>
            <accessor source="#Cube_001-mesh-normals-array" count="6" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="Cube_001-mesh-map-0">
          <float_array id="Cube_001-mesh-map-0-array" count="72">0.7499567 0.2500433 0.9999135 0.5 0.7499568 0.5 8.65876e-5 0.2500433 0.2500433 0.5000001 8.65876e-5 0.5000001 0.2500433 0.2500433 0.5000001 0.5000001 0.2500433 0.5000001 1.000016 0.9946547 -0.001886904 -0.007248461 1.000016 -0.007248759 0.5000001 0.7499568 0.7499568 0.5 0.7499568 0.7499567 0.7499567 8.65876e-5 0.5 0.2500433 0.5 8.66471e-5 0.7499567 0.2500433 0.9999134 0.2500433 0.9999135 0.5 8.65876e-5 0.2500433 0.2500433 0.2500433 0.2500433 0.5000001 0.2500433 0.2500433 0.5 0.2500433 0.5000001 0.5000001 1.000016 0.9946547 -0.001886665 0.9946548 -0.001886904 -0.007248461 0.5000001 0.7499568 0.5000001 0.5000001 0.7499568 0.5 0.7499567 8.65876e-5 0.7499567 0.2500433 0.5 0.2500433</float_array>
          <technique_common>
            <accessor source="#Cube_001-mesh-map-0-array" count="36" stride="2">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <vertices id="Cube_001-mesh-vertices">
          <input semantic="POSITION" source="#Cube_001-mesh-positions"/>
        </vertices>
        <triangles material="SponsorWall-material" count="10">
          <input semantic="VERTEX" source="#Cube_001-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#Cube_001-mesh-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#Cube_001-mesh-map-0" offset="2" set="0"/>
          <p>1 0 0 2 0 1 0 0 2 3 1 3 6 1 4 2 1 5 7 2 6 4 2 7 6 2 8 6 4 12 0 4 13 2 4 14 3 5 15 5 5 16 7 5 17 1 0 18 3 0 19 2 0 20 3 1 21 7 1 22 6 1 23 7 2 24 5 2 25 4 2 26 6 4 30 4 4 31 0 4 32 3 5 33 1 5 34 5 5 35</p>
        </triangles>
        <triangles material="SponsorWallPicture-material" count="2">
          <input semantic="VERTEX" source="#Cube_001-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#Cube_001-mesh-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#Cube_001-mesh-map-0" offset="2" set="0"/>
          <p>5 3 9 0 3 10 4 3 11 5 3 27 1 3 28 0 3 29</p>
        </triangles>
      </mesh>
    </geometry>
    <geometry id="Cube_002-mesh" name="Cube.002">
      <mesh>
        <source id="Cube_002-mesh-positions">
          <float_array id="Cube_002-mesh-positions-array" count="24">-1 -1 -1 -1 -1 1 -1 1 -1 -1 1 1 1 -1 -1 1 -1 1 1 1 -1 1 1 1</float_array>
          <technique_common>
            <accessor source="#Cube_002-mesh-positions-array" count="8" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="Cube_002-mesh-normals">
          <float_array id="Cube_002-mesh-normals-array" count="18">-1 0 0 0 1 0 1 0 0 0 -1 0 0 0 -1 0 0 1</float_array>
          <technique_common>
            <accessor source="#Cube_002-mesh-normals-array" count="6" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="Cube_002-mesh-map-0">
          <float_array id="Cube_002-mesh-map-0-array" count="72">0.2556047 0.7500301 0.1277086 0.9998161 0.1277709 0.7499082 0.9939137 0.9959319 0.003043353 -8.58068e-4 0.9943976 -3.71605e-4 0.2557295 0.2502139 0.1278333 0.5000001 0.1278957 0.250092 0.9481909 0.6319901 0.8202948 0.8817763 0.8203572 0.6318682 0.1278957 0.250092 1.24333e-4 6.19287e-5 0.1279581 1.83893e-4 0.2557919 3.05856e-4 0.3835633 0.2503359 0.2557295 0.2502139 0.2556047 0.7500301 0.2555423 0.9999381 0.1277086 0.9998161 0.9939137 0.9959319 0.002559363 0.9954454 0.003043353 -8.58068e-4 0.2557295 0.2502139 0.2556671 0.500122 0.1278333 0.5000001 0.9481909 0.6319901 0.9481286 0.8818982 0.8202948 0.8817763 0.1278957 0.250092 6.19287e-5 0.2499699 1.24333e-4 6.19287e-5 0.2557919 3.05856e-4 0.3836257 4.27876e-4 0.3835633 0.2503359</float_array>
          <technique_common>
            <accessor source="#Cube_002-mesh-map-0-array" count="36" stride="2">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <vertices id="Cube_002-mesh-vertices">
          <input semantic="POSITION" source="#Cube_002-mesh-positions"/>
        </vertices>
        <triangles material="FetchitWall-material" count="10">
          <input semantic="VERTEX" source="#Cube_002-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#Cube_002-mesh-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#Cube_002-mesh-map-0" offset="2" set="0"/>
          <p>1 0 0 2 0 1 0 0 2 7 2 6 4 2 7 6 2 8 5 3 9 0 3 10 4 3 11 6 4 12 0 4 13 2 4 14 3 5 15 5 5 16 7 5 17 1 0 18 3 0 19 2 0 20 7 2 24 5 2 25 4 2 26 5 3 27 1 3 28 0 3 29 6 4 30 4 4 31 0 4 32 3 5 33 1 5 34 5 5 35</p>
        </triangles>
        <triangles material="FetchitWallPicture-material" count="2">
          <input semantic="VERTEX" source="#Cube_002-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#Cube_002-mesh-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#Cube_002-mesh-map-0" offset="2" set="0"/>
          <p>3 1 3 6 1 4 2 1 5 3 1 21 7 1 22 6 1 23</p>
        </triangles>
      </mesh>
    </geometry>
  </library_geometries>
  <library_controllers/>
  <library_visual_scenes>
    <visual_scene id="Scene" name="Scene">
      <node id="Plane" name="Plane" type="NODE">
        <matrix sid="transform">1.524 0 0 0 0 1.524 0 0 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry url="#Plane-mesh" name="Plane">
          <bind_material>
            <technique_common>
              <instance_material symbol="Floor-material" target="#Floor-material"/>
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
      <node id="Cube" name="Cube" type="NODE">
        <matrix sid="transform">1.524 0 0 0 0 0.005 0 1.524 0 0 1 1 0 0 0 1</matrix>
        <instance_geometry url="#Cube_001-mesh" name="Cube">
          <bind_material>
            <technique_common>
              <instance_material symbol="SponsorWall-material" target="#SponsorWall-material"/>
              <instance_material symbol="SponsorWallPicture-material" target="#SponsorWallPicture-material"/>
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
      <node id="Cube_001" name="Cube_001" type="NODE">
        <matrix sid="transform">-6.66162e-8 -0.005 0 1.524 1.524 -2.18557e-10 0 0 0 0 1 1 0 0 0 1</matrix>
        <instance_geometry url="#Cube_002-mesh" name="Cube_001">
          <bind_material>
            <technique_common>
              <instance_material symbol="FetchitWall-material" target="#FetchitWall-material"/>
              <instance_material symbol="FetchitWallPicture-material" target="#FetchitWallPicture-material"/>
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
    </visual_scene>
  </library_visual_scenes>
  <scene>
    <instance_visual_scene url="#Scene"/>
  </scene>
</COLLADA>