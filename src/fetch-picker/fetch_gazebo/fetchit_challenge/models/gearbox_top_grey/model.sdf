<?xml version="1.0" ?>
<sdf version='1.4'>
  <model name="gearbox_top_grey">
      <static>0</static>
      <link name='link'>
        <inertial>
          <pose>0 0 0 0 0 0</pose>
          <mass>0.2282587</mass>
          <inertia>
            <ixx>4.84526132205e-05</ixx>
            <ixy>0.000000</ixy>
            <ixz>0.000000</ixz>
            <iyy>0.000571913204992</iyy>
            <iyz>0.000000</iyz>
            <izz>0.000580776512074</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <mesh>
              <uri>model://gearbox_top_grey/meshes/gearBoxTop_v3.stl</uri>
              <scale>1 1 1</scale>
            </mesh>
          </geometry>
          <surface>
            <friction>
              <ode>
                <mu>30.0</mu>
                <mu2>30.0</mu2>
              </ode>
            </friction>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <mesh>
              <uri>model://gearbox_top_grey/meshes/gearBoxTop_grey.dae</uri>
              <scale>1 1 1</scale>
            </mesh>
          </geometry>
        </visual>
        <velocity_decay>
          <linear>0.000000</linear>
          <angular>0.000000</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>        
      </link>   
  </model>
</sdf>
