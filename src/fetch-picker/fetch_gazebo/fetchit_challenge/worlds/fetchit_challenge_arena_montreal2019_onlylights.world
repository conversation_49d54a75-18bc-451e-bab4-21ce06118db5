<?xml version="1.0" ?>
<sdf version="1.4">
  <world name="default">

    <include>
      <uri>model://ground_plane</uri>
      <pose>0 0 -0.005 0 0 0</pose>
    </include>

    <include>
      <uri>model://fetchit_arena</uri>
      <name>fetchit_arena1</name>
      <pose>0 0 0 0 0 0</pose>
    </include>

    <light name='user_point_light_0' type='point'>
      <pose frame=''>4.0 4.0 3.5 0 0 0</pose>
      <diffuse>0.7 0.7 0.7 1</diffuse>
      <specular>0.1 0.1 0.1 1</specular>
      <attenuation>
        <range>10</range>
        <constant>1.5</constant>
        <linear>0.01</linear>
        <quadratic>0.001</quadratic>
      </attenuation>
      <cast_shadows>0</cast_shadows>
      <direction>0 0 -1</direction>
    </light>

    <light name='user_point_light_1' type='point'>
      <pose frame=''>-4.0 4.0 3.5 0 0 0</pose>
      <diffuse>0.7 0.7 0.7 1</diffuse>
      <specular>0.1 0.1 0.1 1</specular>
      <attenuation>
        <range>10</range>
        <constant>1.5</constant>
        <linear>0.01</linear>
        <quadratic>0.001</quadratic>
      </attenuation>
      <cast_shadows>0</cast_shadows>
      <direction>0 0 -1</direction>
    </light>

    <light name='user_point_light_2' type='point'>
      <pose frame=''>-4.0 -4.0 3.5 0 0 0</pose>
      <diffuse>0.7 0.7 0.7 1</diffuse>
      <specular>0.1 0.1 0.1 1</specular>
      <attenuation>
        <range>10</range>
        <constant>1.5</constant>
        <linear>0.01</linear>
        <quadratic>0.001</quadratic>
      </attenuation>
      <cast_shadows>0</cast_shadows>
      <direction>0 0 -1</direction>
    </light>


    <light name='user_point_light_3' type='point'>
      <pose frame=''>4.0 -4.0 3.5 0 0 0</pose>
      <diffuse>0.7 0.7 0.7 1</diffuse>
      <specular>0.1 0.1 0.1 1</specular>
      <attenuation>
        <range>10</range>
        <constant>1.5</constant>
        <linear>0.01</linear>
        <quadratic>0.001</quadratic>
      </attenuation>
      <cast_shadows>0</cast_shadows>
      <direction>0 0 -1</direction>
    </light>


  </world>
</sdf>
