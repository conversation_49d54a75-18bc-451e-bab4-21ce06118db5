<?xml version="1.0" ?>
<sdf version="1.4">
  <world name="default">


    <include>
      <uri>model://ground_plane</uri>
    </include>


    <include>
      <uri>model://caddy_green</uri>
      <name>caddy_green1</name>
      <pose>0 0 0 0 0 0</pose>
    </include>

    <include>
      <uri>model://caddy_blue</uri>
      <name>caddy_blue1</name>
      <pose>0.3 0 0 0 0 0</pose>
    </include>

    <include>
      <uri>model://caddy_grey</uri>
      <name>caddy_grey1</name>
      <pose>0.6 0 0 0 0 0</pose>
    </include>

    <include>
      <uri>model://caddy_orange</uri>
      <name>caddy_orange1</name>
      <pose>0.9 0 0 0 0 0</pose>
    </include>

    <include>
      <uri>model://caddy_pink</uri>
      <name>caddy_pink1</name>
      <pose>1.2 0 0 0 0 0</pose>
    </include>

    <include>
      <uri>model://caddy_purple</uri>
      <name>caddy_purple1</name>
      <pose>1.5 0 0 0 0 0</pose>
    </include>

    <include>
      <uri>model://caddy_red</uri>
      <name>caddy_red1</name>
      <pose>1.8 0 0 0 0 0</pose>
    </include>

    <include>
      <uri>model://caddy_yellow</uri>
      <name>caddy_yellow1</name>
      <pose>2.1 0 0 0 0 0</pose>
    </include>



    <light name='user_point_light_0' type='point'>
      <pose frame=''>1.0 1.0 3.5 0 0 0</pose>
      <diffuse>0.7 0.7 0.7 1</diffuse>
      <specular>0.1 0.1 0.1 1</specular>
      <attenuation>
        <range>10</range>
        <constant>0.5</constant>
        <linear>0.01</linear>
        <quadratic>0.001</quadratic>
      </attenuation>
      <cast_shadows>0</cast_shadows>
      <direction>0 0 -1</direction>
    </light>

    <light name='user_point_light_1' type='point'>
      <pose frame=''>-1.0 1.0 3.5 0 0 0</pose>
      <diffuse>0.7 0.7 0.7 1</diffuse>
      <specular>0.1 0.1 0.1 1</specular>
      <attenuation>
        <range>10</range>
        <constant>0.5</constant>
        <linear>0.01</linear>
        <quadratic>0.001</quadratic>
      </attenuation>
      <cast_shadows>0</cast_shadows>
      <direction>0 0 -1</direction>
    </light>

    <light name='user_point_light_2' type='point'>
      <pose frame=''>-1.0 -1.0 3.5 0 0 0</pose>
      <diffuse>0.7 0.7 0.7 1</diffuse>
      <specular>0.1 0.1 0.1 1</specular>
      <attenuation>
        <range>10</range>
        <constant>0.5</constant>
        <linear>0.01</linear>
        <quadratic>0.001</quadratic>
      </attenuation>
      <cast_shadows>0</cast_shadows>
      <direction>0 0 -1</direction>
    </light>


    <light name='user_point_light_3' type='point'>
      <pose frame=''>1.0 -1.0 3.5 0 0 0</pose>
      <diffuse>0.7 0.7 0.7 1</diffuse>
      <specular>0.1 0.1 0.1 1</specular>
      <attenuation>
        <range>10</range>
        <constant>0.5</constant>
        <linear>0.01</linear>
        <quadratic>0.001</quadratic>
      </attenuation>
      <cast_shadows>0</cast_shadows>
      <direction>0 0 -1</direction>
    </light>


  </world>
</sdf>
