<?xml version="1.0"?>
<package format="2">
  <name>fetchit_challenge</name>
  <version>0.9.2</version>
  <description>The fetchit_challenge package</description>

  <author><PERSON></author>
  <maintainer email="<EMAIL>">R<PERSON>aneelOlivaw</maintainer>
  <maintainer email="<EMAIL>"><PERSON><PERSON><PERSON></maintainer>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <maintainer email="csal<PERSON><EMAIL>"><PERSON></maintainer>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <maintainer email="<EMAIL>">Fetch Robotics Open Source Team</maintainer>

  <license>BSD</license>
  <url type="wiki">http://ros.org/wiki/fetch_gazebo</url>
  <url type="website">https://opensource.fetchrobotics.com/competition</url>
  <url type="documentation">https://docs.fetchrobotics.com/gazebo.html#launch-it-on-rosds</url>
  <url type="bugtracker">https://github.com/fetchrobotics/fetch_gazebo/issues</url>
  <url type="repository">https://github.com/fetchrobotics/fetch_gazebo</url>

  <buildtool_depend>catkin</buildtool_depend>
  <build_depend>actionlib</build_depend>
  <build_depend>actionlib_msgs</build_depend>

  <exec_depend>effort_controllers</exec_depend>
  <exec_depend>gazebo_ros</exec_depend>
  <exec_depend>gazebo_ros_control</exec_depend>
  <exec_depend>rospy</exec_depend>
  <exec_depend>robot_state_publisher</exec_depend>
  <exec_depend>controller_manager</exec_depend>
  <exec_depend>fetch_gazebo</exec_depend>
  <exec_depend>actionlib</exec_depend>
  <exec_depend>actionlib_msgs</exec_depend>

  <!-- The export tag contains other, unspecified, tags -->
  <export>
    <gazebo_ros gazebo_model_path="${prefix}/models"/>
  </export>
</package>
