# .yaml config file
#
# The PID gains and controller settings must be saved in a yaml file that gets loaded
# to the param server via the roslaunch file (garbagecollector_control.launch).

  # Publish all joint states -----------------------------------
  # Creates the /joint_states topic necessary in ROS
  joint_state_controller:
    type: joint_state_controller/JointStateController
    publish_rate: 10.0

  # Effort Controllers ---------------------------------------
  lathe_joint_velocity_controller:
    type: effort_controllers/JointVelocityController
    joint: lathe_joint
    pid: {p: 1, i: 0.01, d: 0.01}

  door_joint_velocity_controller:
    type: effort_controllers/JointVelocityController
    joint: door_joint
    pid: {p: 0, i: 0, d: 0}

  lathe_gripper1_joint_position_controller:
    type: effort_controllers/JointPositionController
    joint: lathe_gripper1_joint
    pid: {p: 10000.0, i: 100.0, d: 100.0}

  lathe_gripper2_joint_position_controller:
    type: effort_controllers/JointPositionController
    joint: lathe_gripper2_joint
    pid: {p: 10000.0, i: 100.0, d: 100.0}

  lathe_gripper3_joint_position_controller:
    type: effort_controllers/JointPositionController
    joint: lathe_gripper3_joint
    pid: {p: 10000.0, i: 100.0, d: 100.0}
