<?xml version="1.0" encoding="utf-8"  ?>
<robot name="cartpole_v0">


	<gazebo>
        <plugin name="gazebo_ros_control" filename="libgazebo_ros_control.so">
            <robotNamespace>schunk_machine</robotNamespace>

            <!-- You must spawn this URDF in a ROS namespace, so it doesn't clash with the primary robot -->
            <robotParam>robot_description</robotParam>
        </plugin>
    </gazebo>

	<!-- * * * Link Definitions * * * -->
    <link name="world"/>
	<link name="base_link">
		<visual>
			<origin xyz="0 0 0" rpy="0 0 0"/>
			<geometry>
				<mesh filename="package://fetchit_challenge/models/shunk_machine/meshes/shunk_machine_nodoor_nolathe_v2.dae"/>
			</geometry>
	   </visual>
		<collision>
			<origin xyz="0 0 0" rpy="0 0 0"/>
			<geometry>
				<mesh filename="package://fetchit_challenge/models/shunk_machine/meshes/shunk_machine_nodoor_nolathe_collisions_simple.stl"/>
			</geometry>
	   </collision>
		<inertial>
		    <origin xyz="0 0 0" rpy="0 0 0"/>
        	<mass value="100.0"/>
        	<inertia ixx="21.1118192667" ixy="0.0" ixz="0.0" iyy="15.0520372497" iyz="0.0" izz="14.0746308897"/>
        </inertial>
	</link>
	<gazebo reference="base_link">
		<kp>1000000.0</kp>
        <kd>1000000.0</kd>
        <mu1>30.0</mu1>
        <mu2>30.0</mu2>
    </gazebo>

	<link name="door_link">
		<visual>
			<origin xyz="0 0 0" rpy="0 0 0"/>
			<geometry>
				<mesh filename="package://fetchit_challenge/models/shunk_machine/meshes/shunk_machine_door_transparent_nologo.dae"/>
			</geometry>
	   </visual>
  		<collision>
			<origin xyz="0 0 0" rpy="0 0 0"/>
			<geometry>
				<mesh filename="package://fetchit_challenge/models/shunk_machine/meshes/shunk_machine_door_collisions_simple.stl"/>
			</geometry>
	   </collision>
  		<inertial>
		    <origin xyz="0 0 0" rpy="0 0 0"/>
        	<mass value="1.0"/>
        	<inertia ixx="0.0296795659171" ixy="0.0" ixz="0.0" iyy="0.0245555420748" iyz="0.0" izz="0.0267032688304"/>
        </inertial>
	</link>
	<gazebo reference="door_link">
        <kp>1000.0</kp>
        <kd>1000.0</kd>
        <mu1>0.5</mu1>
        <mu2>0.5</mu2>
    </gazebo>

    <link name="lathe_link">
		<visual>
			<origin xyz="0 0 0" rpy="0 0 0"/>
			<geometry>
				<mesh filename="package://fetchit_challenge/models/shunk_machine/meshes/shunk_machine_lathe_centered_base.dae"/>
			</geometry>
	   </visual>
		<collision>
			<origin xyz="0 0 0" rpy="0 0 0"/>
			<geometry>
				<mesh filename="package://fetchit_challenge/models/shunk_machine/meshes/shunk_machine_lathe_base_joint_colision.stl"/>
			</geometry>
	   </collision>
  		<inertial>
		    <origin xyz="0 0 0" rpy="0 0 0"/>
        	<mass value="2.5"/>
        	<inertia ixx="0.0166666666667" ixy="0.0" ixz="0.0" iyy="0.0416666666667" iyz="0.0" izz="0.0416666666667"/>
        </inertial>
	</link>
	<gazebo reference="lathe_link">
        <kp>1000.0</kp>
        <kd>1000.0</kd>
        <mu1>0.5</mu1>
        <mu2>0.5</mu2>
    </gazebo>


	<link name="lathe_gripper1_link">
		<visual>
			<origin xyz="0 0 0" rpy="0 0 0"/>
			<geometry>
				<mesh filename="package://fetchit_challenge/models/shunk_machine/meshes/shunk_machine_lathe_gripper.dae"/>
			</geometry>
	   </visual>
		<collision>
			<origin xyz="0 0 0" rpy="0 0 0"/>
			<geometry>
				<mesh filename="package://fetchit_challenge/models/shunk_machine/meshes/shunk_machine_lathe_gripper_collision.stl"/>
			</geometry>
	   </collision>
  		<inertial>
		    <origin xyz="0 0 0" rpy="0 0 0"/>
        	<mass value="0.5"/>
        	<inertia ixx="6.46237297354e-05" ixy="0.0" ixz="0.0" iyy="0.000163168296934" iyz="0.0" izz="0.000152952440802"/>
        </inertial>
	</link>
	<gazebo reference="lathe_gripper1_link">
        <kp>1000.0</kp>
        <kd>1000.0</kd>
        <mu1>1000.0</mu1>
        <mu2>1000.0</mu2>
    </gazebo>

	<link name="lathe_gripper2_link">
		<visual>
			<origin xyz="0 0 0" rpy="0 0 0"/>
			<geometry>
				<mesh filename="package://fetchit_challenge/models/shunk_machine/meshes/shunk_machine_lathe_gripper.dae"/>
			</geometry>
	   </visual>
		<collision>
			<origin xyz="0 0 0" rpy="0 0 0"/>
			<geometry>
				<mesh filename="package://fetchit_challenge/models/shunk_machine/meshes/shunk_machine_lathe_gripper_collision.stl"/>
			</geometry>
	   </collision>
  		<inertial>
		    <origin xyz="0 0 0" rpy="0 0 0"/>
        	<mass value="0.5"/>
        	<inertia ixx="6.46237297354e-05" ixy="0.0" ixz="0.0" iyy="0.000163168296934" iyz="0.0" izz="0.000152952440802"/>
        </inertial>
	</link>
	<gazebo reference="lathe_gripper2_link">
        <kp>1000.0</kp>
        <kd>1000.0</kd>
        <mu1>1000.0</mu1>
        <mu2>1000.0</mu2>
    </gazebo>


	<link name="lathe_gripper3_link">
		<visual>
			<origin xyz="0 0 0" rpy="0 0 0"/>
			<geometry>
				<mesh filename="package://fetchit_challenge/models/shunk_machine/meshes/shunk_machine_lathe_gripper.dae"/>
			</geometry>
	   </visual>
		<collision>
			<origin xyz="0 0 0" rpy="0 0 0"/>
			<geometry>
				<mesh filename="package://fetchit_challenge/models/shunk_machine/meshes/shunk_machine_lathe_gripper_collision.stl"/>
			</geometry>
	   </collision>
  		<inertial>
		    <origin xyz="0 0 0" rpy="0 0 0"/>
        	<mass value="0.5"/>
        	<inertia ixx="6.46237297354e-05" ixy="0.0" ixz="0.0" iyy="0.000163168296934" iyz="0.0" izz="0.000152952440802"/>
        </inertial>
	</link>
	<gazebo reference="lathe_gripper3_link">
        <kp>1000.0</kp>
        <kd>1000.0</kd>
        <mu1>1000.0</mu1>
        <mu2>1000.0</mu2>
    </gazebo>

<!-- * * * Joint Definitions * * * -->
	<joint name="lathe_gripper1_joint" type="prismatic">
    	<parent link="lathe_link"/>
    	<child link="lathe_gripper1_link"/>
		<origin xyz="0.072422 0.0932665 0.0" rpy="0 3.1416 0"/>
       	<limit lower="0.0" upper="0.02" effort="100.0" velocity="1.0"/>
		<dynamics damping="0.0" friction="0.1"/>
		<axis xyz="1 0 0"/>
	</joint>

	<joint name="lathe_gripper2_joint" type="prismatic">
    	<parent link="lathe_link"/>
    	<child link="lathe_gripper2_link"/>
		<origin xyz="-0.036381 0.0932665 0.0635819" rpy="0 1.0472 0"/>
       	<limit lower="0.0" upper="0.02" effort="100.0" velocity="1.0"/>
		<dynamics damping="0.0" friction="0.1"/>
		<axis xyz="1 0 0"/>
	</joint>

	<joint name="lathe_gripper3_joint" type="prismatic">
    	<parent link="lathe_link"/>
    	<child link="lathe_gripper3_link"/>
		<origin xyz="-0.036381 0.0932665 -0.0635819" rpy="0 -1.0472 0"/>
       	<limit lower="0.0" upper="0.02" effort="100.0" velocity="1.0"/>
		<dynamics damping="0.0" friction="0.1"/>
		<axis xyz="1 0 0"/>
	</joint>

 	<joint name="lathe_joint" type="continuous">
    	<parent link="base_link"/>
    	<child link="lathe_link"/>
    	<origin xyz="-0.194999 -0.108002 0.979988" rpy="0 0 0"/>
		<dynamics damping="0.0" friction="0.1"/>
       	<limit effort="1" velocity="100"/>
        <axis xyz="0 1 0"/>
	</joint>
	<joint name="door_joint" type="prismatic">
    	<parent link="base_link"/>
    	<child link="door_link"/>
       	<limit lower="0.0" upper="0.445903" effort="1.0" velocity="1.0"/>
		<dynamics damping="0.0" friction="0.1"/>
		<axis xyz="0 1 0"/>
	</joint>
	<joint name="fixed" type="fixed">
        <parent link="world"/>
        <child link="base_link"/>
    </joint>

<!-- * * * Transmission Definitions * * * -->
	<transmission name="lathe_gripper1_joint_trans">
      <type>transmission_interface/SimpleTransmission</type>
      <joint name="lathe_gripper1_joint">
        <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
      </joint>
      <actuator name="lathe_gripper1_jointMotor">
        <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
        <mechanicalReduction>1</mechanicalReduction>
      </actuator>
    </transmission>


	<transmission name="lathe_gripper2_joint_trans">
      <type>transmission_interface/SimpleTransmission</type>
      <joint name="lathe_gripper2_joint">
        <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
      </joint>
      <actuator name="lathe_gripper2_jointMotor">
        <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
        <mechanicalReduction>1</mechanicalReduction>
      </actuator>
    </transmission>


	<transmission name="lathe_gripper3_joint_trans">
      <type>transmission_interface/SimpleTransmission</type>
      <joint name="lathe_gripper3_joint">
        <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
      </joint>
      <actuator name="lathe_gripper3_jointMotor">
        <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
        <mechanicalReduction>1</mechanicalReduction>
      </actuator>
    </transmission>


	<transmission name="lathe_joint_trans">
      <type>transmission_interface/SimpleTransmission</type>
      <joint name="lathe_joint">
        <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
      </joint>
      <actuator name="lathe_jointMotor">
        <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
        <mechanicalReduction>1</mechanicalReduction>
      </actuator>
    </transmission>


	<transmission name="door_joint_trans">
      <type>transmission_interface/SimpleTransmission</type>
      <joint name="door_joint">
        <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
      </joint>
      <actuator name="door_jointMotor">
        <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
        <mechanicalReduction>1</mechanicalReduction>
      </actuator>
    </transmission>




</robot>