<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>fetch_simulation</name>
  <version>0.9.2</version>
  <description>Fetch Simulation, packages for working with <PERSON><PERSON> and <PERSON><PERSON><PERSON> in Gazebo</description>
  <maintainer email="<EMAIL>">Niharika Arora</maintainer>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <maintainer email="<EMAIL>">Fetch Robotics Open Source Team</maintainer>

  <license>BSD</license>
  <url type="website">https://docs.fetchrobotics.com/gazebo.html</url>
  <url type="repository">https://github.com/fetchrobotics/fetch_gazebo</url>
  <url type="bugtracker">https://github.com/fetchrobotics/fetch_gazebo/issues</url>
  <url type="wiki">https://wiki.ros.org/fetch_simulation</url>

  <author>Alex Moriarty</author>

  <buildtool_depend>catkin</buildtool_depend>

  <exec_depend>fetch_gazebo</exec_depend>
  <exec_depend>fetch_gazebo_demo</exec_depend>
  <exec_depend>fetchit_challenge</exec_depend>

  <export>
    <metapackage/>
  </export>
</package>
