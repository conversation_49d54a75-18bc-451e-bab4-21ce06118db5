<package>
  <name>fetch_gazebo_demo</name>
  <version>0.9.2</version>
  <description>
    Demos for fetch_gazebo package.
  </description>
  <author><PERSON></author>
  <maintainer email="<EMAIL>"><PERSON><PERSON><PERSON></maintainer>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <maintainer email="c<PERSON><PERSON><PERSON>@fetchrobotics.com"><PERSON></maintainer>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <maintainer email="<EMAIL>">Fetch Robotics Open Source Team</maintainer>

  <license>BSD</license>
  <url>http://ros.org/wiki/fetch_gazebo_demo</url>
  
  <buildtool_depend>catkin</buildtool_depend>

  <run_depend>actionlib</run_depend>
  <run_depend>fetch_gazebo</run_depend>
  <run_depend>fetch_moveit_config</run_depend>
  <run_depend>fetch_navigation</run_depend>
  <run_depend>moveit_commander</run_depend>
  <run_depend>moveit_python</run_depend>
  <run_depend>simple_grasping</run_depend>
  <run_depend>teleop_twist_keyboard</run_depend>
</package>
