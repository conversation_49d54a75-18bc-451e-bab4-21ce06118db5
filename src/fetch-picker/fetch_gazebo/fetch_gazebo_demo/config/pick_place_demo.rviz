Panels:
  - Class: rviz/Displays
    Help Height: 0
    Name: Displays
    Property Tree Widget:
      Expanded:
        - /Global Options1
      Splitter Ratio: 0.684211
    Tree Height: 721
  - Class: rviz/Selection
    Name: Selection
  - Class: rviz/Tool Properties
    Expanded:
      - /2D Pose Estimate1
      - /2D Nav Goal1
      - /Publish Point1
    Name: Tool Properties
    Splitter Ratio: 0.588679
  - Class: rviz/Views
    Expanded:
      - /Current View1
    Name: Views
    Splitter Ratio: 0.5
  - Class: rviz/Time
    Experimental: false
    Name: Time
    SyncMode: 0
    SyncSource: PointCloud2
Visualization Manager:
  Class: ""
  Displays:
    - Alpha: 0.5
      Cell Size: 1
      Class: rviz/Grid
      Color: 160; 160; 164
      Enabled: true
      Line Style:
        Line Width: 0.03
        Value: Lines
      Name: Grid
      Normal Cell Count: 0
      Offset:
        X: 0
        Y: 0
        Z: 0
      Plane: XY
      Plane Cell Count: 10
      Reference Frame: <Fixed Frame>
      Value: true
    - Alpha: 1
      Class: rviz/RobotModel
      Collision Enabled: false
      Enabled: true
      Links:
        All Links Enabled: true
        Expand Joint Details: false
        Expand Link Details: false
        Expand Tree: false
        Link Tree Style: Links in Alphabetic Order
        base_link:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        bellows_link:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        bellows_link2:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        elbow_flex_link:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        estop_link:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        forearm_roll_link:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        gripper_link:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        head_camera_depth_frame:
          Alpha: 1
          Show Axes: false
          Show Trail: false
        head_camera_depth_optical_frame:
          Alpha: 1
          Show Axes: false
          Show Trail: false
        head_camera_link:
          Alpha: 1
          Show Axes: false
          Show Trail: false
        head_camera_rgb_frame:
          Alpha: 1
          Show Axes: false
          Show Trail: false
        head_camera_rgb_optical_frame:
          Alpha: 1
          Show Axes: false
          Show Trail: false
        head_pan_link:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        head_tilt_link:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        l_gripper_finger_link:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        l_wheel_link:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        laser_link:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        r_gripper_finger_link:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        r_wheel_link:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        shoulder_lift_link:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        shoulder_pan_link:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        torso_fixed_link:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        torso_lift_link:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        upperarm_roll_link:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        wrist_flex_link:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        wrist_roll_link:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
      Name: RobotModel
      Robot Description: robot_description
      TF Prefix: ""
      Update Interval: 0
      Value: true
      Visual Enabled: true
    - Alpha: 1
      Autocompute Intensity Bounds: true
      Autocompute Value Bounds:
        Max Value: 10
        Min Value: -10
        Value: true
      Axis: Z
      Channel Name: intensity
      Class: rviz/PointCloud2
      Color: 255; 255; 255
      Color Transformer: RGB8
      Decay Time: 0
      Enabled: true
      Invert Rainbow: false
      Max Color: 255; 255; 255
      Max Intensity: 4096
      Min Color: 0; 0; 0
      Min Intensity: 0
      Name: PointCloud2
      Position Transformer: XYZ
      Queue Size: 10
      Selectable: true
      Size (Pixels): 3
      Size (m): 0.01
      Style: Points
      Topic: /head_camera/depth_registered/points
      Unreliable: false
      Use Fixed Frame: true
      Use rainbow: true
      Value: true
    - Alpha: 1
      Autocompute Intensity Bounds: true
      Autocompute Value Bounds:
        Max Value: 10
        Min Value: -10
        Value: true
      Axis: Z
      Channel Name: intensity
      Class: rviz/LaserScan
      Color: 255; 255; 255
      Color Transformer: Intensity
      Decay Time: 0
      Enabled: true
      Invert Rainbow: false
      Max Color: 255; 255; 255
      Max Intensity: 999999
      Min Color: 0; 0; 0
      Min Intensity: 0
      Name: LaserScan
      Position Transformer: XYZ
      Queue Size: 10
      Selectable: true
      Size (Pixels): 3
      Size (m): 0.01
      Style: Flat Squares
      Topic: /base_scan
      Unreliable: false
      Use Fixed Frame: true
      Use rainbow: true
      Value: true
    - Class: moveit_rviz_plugin/MotionPlanning
      Enabled: false
      Move Group Namespace: ""
      MoveIt_Goal_Tolerance: 0
      MoveIt_Planning_Attempts: 10
      MoveIt_Planning_Time: 5
      MoveIt_Use_Constraint_Aware_IK: true
      MoveIt_Warehouse_Host: 127.0.0.1
      MoveIt_Warehouse_Port: 33829
      MoveIt_Workspace:
        Center:
          X: 0
          Y: 0
          Z: 0
        Size:
          X: 2
          Y: 2
          Z: 2
      Name: MotionPlanning
      Planned Path:
        Interrupt Display: false
        Links:
          All Links Enabled: true
          Expand Joint Details: false
          Expand Link Details: false
          Expand Tree: false
          Link Tree Style: Links in Alphabetic Order
          base_link:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          bellows_link:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          bellows_link2:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          elbow_flex_link:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          estop_link:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          forearm_roll_link:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          gripper_link:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          head_camera_depth_frame:
            Alpha: 1
            Show Axes: false
            Show Trail: false
          head_camera_depth_optical_frame:
            Alpha: 1
            Show Axes: false
            Show Trail: false
          head_camera_link:
            Alpha: 1
            Show Axes: false
            Show Trail: false
          head_camera_rgb_frame:
            Alpha: 1
            Show Axes: false
            Show Trail: false
          head_camera_rgb_optical_frame:
            Alpha: 1
            Show Axes: false
            Show Trail: false
          head_pan_link:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          head_tilt_link:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          l_gripper_finger_link:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          l_wheel_link:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          laser_link:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          r_gripper_finger_link:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          r_wheel_link:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          shoulder_lift_link:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          shoulder_pan_link:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          torso_fixed_link:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          torso_lift_link:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          upperarm_roll_link:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          wrist_flex_link:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          wrist_roll_link:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
        Loop Animation: false
        Robot Alpha: 0.5
        Show Robot Collision: false
        Show Robot Visual: true
        Show Trail: false
        State Display Time: 0.05 s
        Trajectory Topic: /move_group/display_planned_path
      Planning Metrics:
        Payload: 1
        Show Joint Torques: false
        Show Manipulability: false
        Show Manipulability Index: false
        Show Weight Limit: false
        TextHeight: 0.08
      Planning Request:
        Colliding Link Color: 255; 0; 0
        Goal State Alpha: 1
        Goal State Color: 250; 128; 0
        Interactive Marker Size: 0
        Joint Violation Color: 255; 0; 255
        Planning Group: arm
        Query Goal State: true
        Query Start State: true
        Show Workspace: false
        Start State Alpha: 1
        Start State Color: 0; 255; 0
      Planning Scene Topic: planning_scene
      Robot Description: robot_description
      Scene Geometry:
        Scene Alpha: 0.9
        Scene Color: 50; 230; 50
        Scene Display Time: 0.2
        Show Scene Geometry: true
        Voxel Coloring: Z-Axis
        Voxel Rendering: Occupied Voxels
      Scene Robot:
        Attached Body Color: 150; 50; 150
        Links:
          All Links Enabled: true
          Expand Joint Details: false
          Expand Link Details: false
          Expand Tree: false
          Link Tree Style: Links in Alphabetic Order
          base_link:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          bellows_link:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          bellows_link2:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          elbow_flex_link:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          estop_link:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          forearm_roll_link:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          gripper_link:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          head_camera_depth_frame:
            Alpha: 1
            Show Axes: false
            Show Trail: false
          head_camera_depth_optical_frame:
            Alpha: 1
            Show Axes: false
            Show Trail: false
          head_camera_link:
            Alpha: 1
            Show Axes: false
            Show Trail: false
          head_camera_rgb_frame:
            Alpha: 1
            Show Axes: false
            Show Trail: false
          head_camera_rgb_optical_frame:
            Alpha: 1
            Show Axes: false
            Show Trail: false
          head_pan_link:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          head_tilt_link:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          l_gripper_finger_link:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          l_wheel_link:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          laser_link:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          r_gripper_finger_link:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          r_wheel_link:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          shoulder_lift_link:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          shoulder_pan_link:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          torso_fixed_link:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          torso_lift_link:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          upperarm_roll_link:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          wrist_flex_link:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          wrist_roll_link:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
        Robot Alpha: 1
        Show Robot Collision: false
        Show Robot Visual: true
      Value: false
  Enabled: true
  Global Options:
    Background Color: 48; 48; 48
    Fixed Frame: odom
    Frame Rate: 30
  Name: root
  Tools:
    - Class: rviz/Interact
      Hide Inactive Objects: true
    - Class: rviz/MoveCamera
    - Class: rviz/Select
    - Class: rviz/FocusCamera
    - Class: rviz/Measure
    - Class: rviz/SetInitialPose
      Topic: /initialpose
    - Class: rviz/SetGoal
      Topic: /move_base_simple/goal
    - Class: rviz/PublishPoint
      Single click: true
      Topic: /clicked_point
  Value: true
  Views:
    Current:
      Class: rviz/Orbit
      Distance: 3.93618
      Enable Stereo Rendering:
        Stereo Eye Separation: 0.06
        Stereo Focal Distance: 1
        Swap Stereo Eyes: false
        Value: false
      Focal Point:
        X: 0
        Y: 0
        Z: 0
      Name: Current View
      Near Clip Distance: 0.01
      Pitch: 0.635398
      Target Frame: <Fixed Frame>
      Value: Orbit (rviz)
      Yaw: 0.0204
    Saved: ~
Window Geometry:
  Displays:
    collapsed: false
  Height: 923
  Hide Left Dock: false
  Hide Right Dock: false
  Motion Planning:
    collapsed: false
  QMainWindow State: 000000ff00000000fd00000004000000000000016b00000311fc0200000009fb0000001200530065006c0065006300740069006f006e00000001e10000009b0000006100fffffffb0000001e0054006f006f006c002000500072006f007000650072007400690065007302000001ed000001df00000185000000a3fb000000120056006900650077007300200054006f006f02000001df000002110000018500000122fb000000200054006f006f006c002000500072006f0070006500720074006900650073003203000002880000011d000002210000017afb000000100044006900730070006c006100790073010000002800000311000000d600fffffffb0000002000730065006c0065006300740069006f006e00200062007500660066006500720200000138000000aa0000023a00000294fb00000014005700690064006500530074006500720065006f02000000e6000000d2000003ee0000030bfb0000000c004b0069006e0065006300740200000186000001060000030c00000261fb0000001e004d006f00740069006f006e00200050006c0061006e006e0069006e00670000000184000001b50000001600000016000000010000010f00000311fc0200000003fb0000001e0054006f006f006c002000500072006f00700065007200740069006500730100000041000000780000000000000000fb0000000a00560069006500770073010000002800000311000000ac00fffffffb0000001200530065006c0065006300740069006f006e010000025a000000b200000000000000000000000200000490000000a9fc0100000001fb0000000a00560069006500770073030000004e00000080000002e10000019700000003000006720000003efc0100000002fb0000000800540069006d0065010000000000000672000002f500fffffffb0000000800540069006d00650100000000000004500000000000000000000003ec0000031100000004000000040000000800000008fc0000000100000002000000010000000a0054006f006f006c00730100000000ffffffff0000000000000000
  Selection:
    collapsed: false
  Time:
    collapsed: false
  Tool Properties:
    collapsed: false
  Views:
    collapsed: false
  Width: 1650
  X: 102
  Y: 77
