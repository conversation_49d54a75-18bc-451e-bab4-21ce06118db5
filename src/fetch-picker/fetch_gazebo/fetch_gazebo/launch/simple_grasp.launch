<launch>

  <env name="GAZEBO_MODEL_PATH" value="$(find fetch_gazebo)/models:$(optenv GAZEBO_MODEL_PATH)" />

  <arg name="robot" default="fetch"/>
  <arg name="debug" default="false"/>
  <arg name="gui" default="true"/>
  <arg name="headless" default="false"/>

  <!-- Start Gazebo with a blank world -->
  <include file="$(find gazebo_ros)/launch/empty_world.launch">
    <arg name="debug" value="$(arg debug)" />
    <arg name="gui" value="$(arg gui)" />
    <arg name="paused" value="false"/>
    <arg name="use_sim_time" value="true"/>
    <arg name="headless" value="$(arg headless)"/>
    <arg name="world_name" value="$(find fetch_gazebo)/worlds/simple_pick_place.sdf"/>
  </include>

  <!-- Oh, you wanted a robot? -->
  <include file="$(find fetch_gazebo)/launch/include/$(arg robot)_pp.launch.xml" />

</launch>
