<?xml version="1.0" ?>
<sdf version='1.4'>
  <model name="demo_cube">      
      <static>0</static>
      <link name='link'>
        <inertial>
          <mass>0.25</mass>
          <inertia>
            <ixx>0.001</ixx>
            <ixy>0.000000</ixy>
            <ixz>0.000000</ixz>
            <iyy>0.001</iyy>
            <iyz>0.000000</iyz>
            <izz>0.001</izz>
          </inertia>
        </inertial>
        <collision name='collision'>
          <geometry>
            <box>
              <size>0.060 0.060 0.060</size>
            </box>
          </geometry>
          <surface>
            <friction>
              <ode>
                <mu>30.0</mu>
                <mu2>30.0</mu2>
              </ode>
            </friction>
            <contact>
              <ode>
                <kp>1000000.0</kp>
                <kd>100.0</kd>
                <max_vel>1.0</max_vel>
                <min_depth>0.002</min_depth>
              </ode>
            </contact>
          </surface>
        </collision>
        <visual name='visual'>
          <geometry>
            <box>
              <size>0.060 0.060 0.060</size>
            </box>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Blue</name>
            </script>
          </material>
        </visual>
        <velocity_decay>
          <linear>0.000000</linear>
          <angular>0.000000</angular>
        </velocity_decay>
        <self_collide>0</self_collide>
        <kinematic>0</kinematic>
        <gravity>1</gravity>        
      </link>   
  </model>
</sdf>
