<?xml version="1.0"?>
<package format="2">
  <name>fetch_gazebo</name>
  <version>0.9.2</version>
  <description>
    Gazebo package for Fetch.
  </description>
  <author><PERSON></author>
  <maintainer email="<EMAIL>"><PERSON><PERSON><PERSON></maintainer>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <maintainer email="c<PERSON><PERSON><PERSON>@fetchrobotics.com"><PERSON></maintainer>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <maintainer email="<EMAIL>">Fetch Robotics Open Source Team</maintainer>

  <license>BSD</license>
  <url>http://ros.org/wiki/fetch_gazebo</url>

  <buildtool_depend>catkin</buildtool_depend>

  <build_depend>angles</build_depend>
  <build_depend>gazebo_dev</build_depend>

  <depend>control_toolbox</depend>
  <depend>boost</depend>
  <depend>gazebo_plugins</depend>
  <depend>gazebo_ros</depend>
  <depend>robot_controllers</depend>
  <depend>robot_controllers_interface</depend>
  <depend>sensor_msgs</depend>

  <exec_depend>actionlib</exec_depend>
  <exec_depend>control_msgs</exec_depend>
  <exec_depend>depth_image_proc</exec_depend>
  <exec_depend>fetch_description</exec_depend>
  <exec_depend>gazebo</exec_depend>
  <exec_depend>image_proc</exec_depend>
  <exec_depend>nodelet</exec_depend>
  <exec_depend>rgbd_launch</exec_depend>
  <exec_depend>trajectory_msgs</exec_depend>
  <exec_depend>xacro</exec_depend>
  <exec_depend>robot_state_publisher</exec_depend>
  <exec_depend>topic_tools</exec_depend>
</package>
