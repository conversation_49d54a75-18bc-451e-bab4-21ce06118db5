arm_controller:
  follow_joint_trajectory:
    type: "robot_controllers/FollowJointTrajectoryController"
    joints:
      - shoulder_pan_joint
      - shoulder_lift_joint
      - upperarm_roll_joint
      - elbow_flex_joint
      - forearm_roll_joint
      - wrist_flex_joint
      - wrist_roll_joint
  gravity_compensation:
    type: "robot_controllers/GravityCompensation"
    root: "torso_lift_link"
    tip: "gripper_link"
    autostart: true

arm_with_torso_controller:
  follow_joint_trajectory:
    type: "robot_controllers/FollowJointTrajectoryController"
    joints:
      - torso_lift_joint
      - shoulder_pan_joint
      - shoulder_lift_joint
      - upperarm_roll_joint
      - elbow_flex_joint
      - forearm_roll_joint
      - wrist_flex_joint
      - wrist_roll_joint

torso_controller:
  follow_joint_trajectory:
    type: "robot_controllers/FollowJointTrajectoryController"
    joints:
      - torso_lift_joint

head_controller:
  point_head:
    type: "robot_controllers/PointHeadController"
  follow_joint_trajectory:
    type: "robot_controllers/FollowJointTrajectoryController"
    joints:
      - head_pan_joint
      - head_tilt_joint

base_controller:
  type: "robot_controllers/DiffDriveBaseController"
  autostart: true

gripper_controller:
  gripper_action:
    type: "robot_controllers/ParallelGripperController"
    centering:
      p: 1000.0
      d: 0.0
      i: 0.0
      i_clamp: 0.0

bellows_controller:
  type: "robot_controllers/ScaledMimicController"
  mimic_joint: "torso_lift_joint"
  controlled_joint: "bellows_joint"
  mimic_scale: 0.5
  autostart: true

gazebo:
  default_controllers:
    - "arm_controller/follow_joint_trajectory"
    - "arm_controller/gravity_compensation"
    - "arm_with_torso_controller/follow_joint_trajectory"
    - "base_controller"
    - "head_controller/follow_joint_trajectory"
    - "head_controller/point_head"
    - "torso_controller/follow_joint_trajectory"
    - "gripper_controller/gripper_action"
    - "bellows_controller"
  l_wheel_joint:
    position:
      p: 0.0
      d: 0.0
      i: 0.0
      i_clamp: 0.0
    velocity:
      p: 8.85
      d: 0.0
      i: 0.5
      i_clamp: 6.0
  r_wheel_joint:
    position:
      p: 0.0
      d: 0.0
      i: 0.0
      i_clamp: 0.0
    velocity:
      p: 8.85
      d: 0.0
      i: 0.5
      i_clamp: 6.0
  torso_lift_joint:
    position:
      p: 1000.0
      d: 0.0
      i: 0.0
      i_clamp: 0.0
    velocity:
      p: 100000.0
      d: 0.0
      i: 0.0
      i_clamp: 0.0
  bellows_joint:
    position:
      p: 10.0
      d: 0.0
      i: 0.0
      i_clamp: 0.0
    velocity:
      p: 25.0
      d: 0.0
      i: 0.0
      i_clamp: 0.0
  head_pan_joint:
    position:
      p: 2.0
      d: 0.0
      i: 0.0
      i_clamp: 0.0
    velocity:
      p: 2.0
      d: 0.0
      i: 0.0
      i_clamp: 0.0
  head_tilt_joint:
    position:
      p: 10.0
      d: 0.0
      i: 0.0
      i_clamp: 0.0
    velocity:
      p: 3.0
      d: 0.0
      i: 0.0
      i_clamp: 0.0
  shoulder_pan_joint:
    position:
      p: 100.0
      d: 0.1
      i: 0.0
      i_clamp: 0.0
    velocity:
      p: 200.0
      d: 0.0
      i: 2.0
      i_clamp: 1.0
  shoulder_lift_joint:
    position:
      p: 100.0
      d: 0.1
      i: 0.0
      i_clamp: 0.0
    velocity:
      p: 200.0
      d: 0.0
      i: 0.0
      i_clamp: 0.0
  upperarm_roll_joint:
    position:
      p: 100.0
      d: 0.1
      i: 0.0
      i_clamp: 0.0
    velocity:
      p: 10.0
      d: 0.0
      i: 0.0
      i_clamp: 0.0
  elbow_flex_joint:
    position:
      p: 100.0
      d: 0.1
      i: 0.0
      i_clamp: 0.0
    velocity:
      p: 150.0
      d: 0.0
      i: 0.0
      i_clamp: 0.0
  forearm_roll_joint:
    position:
      p: 100.0
      d: 0.1
      i: 0.0
      i_clamp: 0.0
    velocity:
      p: 150.0
      d: 0.0
      i: 0.0
      i_clamp: 0.0
  wrist_flex_joint:
    position:
      p: 100.0
      d: 0.1
      i: 0.0
      i_clamp: 0.0
    velocity:
      p: 100.0
      d: 0.0
      i: 0.0
      i_clamp: 0.0
  wrist_roll_joint:
    position:
      p: 100.0
      d: 0.1
      i: 0.0
      i_clamp: 0.0
    velocity:
      p: 100.0
      d: 0.0
      i: 0.0
      i_clamp: 0.0
  l_gripper_finger_joint:
    position:
      p: 5000.0
      d: 0.0
      i: 0.0
      i_clamp: 0.0
    velocity:
      p: 0.0
      d: 0.0
      i: 0.0
      i_clamp: 0.0
  r_gripper_finger_joint:
    position:
      p: 5000.0
      d: 0.0
      i: 0.0
      i_clamp: 0.0
    velocity:
      p: 0.0
      d: 0.0
      i: 0.0
      i_clamp: 0.0
