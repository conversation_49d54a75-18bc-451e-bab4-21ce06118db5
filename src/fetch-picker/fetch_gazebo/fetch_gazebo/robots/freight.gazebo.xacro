<robot xmlns:xacro="http://www.ros.org/wiki/xacro" name="freight" >
  <xacro:include filename="$(find fetch_description)/robots/freight.urdf" />

  <!-- Base is modeled as a big tub sitting on floor, with two wheels -->
  <gazebo reference="base_link">
    <kp>100000000.0</kp>
    <kd>10.0</kd>
    <mu1>0.1</mu1>
    <mu2>0.1</mu2>
    <fdir1>1 0 0</fdir1>
    <maxVel>10.0</maxVel>
    <minDepth>0.0005</minDepth>
  </gazebo>
  <gazebo reference="r_wheel_link">
    <kp>500000.0</kp>
    <kd>10.0</kd>
    <mu1>10</mu1>
    <mu2>10</mu2>
    <fdir1>1 0 0</fdir1>
    <maxVel>1.0</maxVel>
    <minDepth>0.003</minDepth>
    <material>Gazebo/Black</material>
  </gazebo>
  <gazebo reference="l_wheel_link">
    <kp>500000.0</kp>
    <kd>10.0</kd>
    <mu1>10</mu1>
    <mu2>10</mu2>
    <fdir1>1 0 0</fdir1>
    <maxVel>1.0</maxVel>
    <minDepth>0.003</minDepth>
    <material>Gazebo/Black</material>
  </gazebo>
  <gazebo reference="l_wheel_joint">
    <implicitSpringDamper>1</implicitSpringDamper>
  </gazebo>
  <gazebo reference="r_wheel_joint">
    <implicitSpringDamper>1</implicitSpringDamper>
  </gazebo>

  <!-- SICK TIM561 (25m Range) -->
  <gazebo reference="laser_link">
    <sensor type="ray" name="base_laser">
      <pose>0 0 0 0 0 0</pose>
      <visualize>true</visualize>
      <update_rate>15</update_rate>
      <ray>
        <scan>
          <horizontal>
            <samples>662</samples>
            <resolution>1</resolution>
            <min_angle>-1.91986</min_angle>
            <max_angle>1.91986</max_angle>
          </horizontal>
        </scan>
        <range>
          <min>0.05</min>
          <max>25.0</max>
          <resolution>0.01</resolution>
        </range>
        <noise>
          <!-- Noise parameters based on spec for SICK TIM561 (10m version) -->
          <type>gaussian</type>
          <mean>0.0</mean>
          <stddev>0.02</stddev>
        </noise>
      </ray>
      <plugin name="gazebo_ros_base_hokuyo_controller" filename="libgazebo_ros_laser.so">
        <topicName>/base_scan</topicName>
        <frameName>laser_link</frameName>
      </plugin>
    </sensor>
  </gazebo>

  <!-- Color the estop -->
  <gazebo reference="estop_link">
    <material>Gazebo/Red</material>
  </gazebo>

  <!-- Load the plugin -->
  <gazebo>
    <plugin name="fetch_gazebo_plugin" filename="libfetch_gazebo_plugin.so"/>
  </gazebo>

</robot>
