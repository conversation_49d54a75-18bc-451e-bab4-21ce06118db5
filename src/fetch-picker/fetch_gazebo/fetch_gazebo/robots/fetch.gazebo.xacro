<robot xmlns:xacro="http://www.ros.org/wiki/xacro" name="fetch" >
  <xacro:include filename="$(find fetch_description)/robots/fetch.urdf" />

  <!-- Add four casters to the base -->
  <xacro:macro name="caster" params="prefix joint_x joint_y">
  <link name="${prefix}_caster_link">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.0601"/> <!-- 0.06033 -->
      </geometry>
      <material name="">
        <color rgba="0.086 0.506 0.767 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.0601"/>
      </geometry>
    </collision>
    <inertial>
      <inertia ixx="1" ixy="0" ixz="0" iyy="1" iyz="0" izz="1"/>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="0.01"/>
    </inertial>
  </link>
  <gazebo reference="${prefix}_caster_link">
    <mu1>0.1</mu1>
    <mu2>0.1</mu2>
    <kp>100000000</kp>
    <kd>10.0</kd>
    <maxVel>10</maxVel>
    <minDepth>0.001</minDepth>
  </gazebo>
  <joint name="${prefix}_caster_joint" type="fixed">
    <parent link="base_link"/>
    <child link="${prefix}_caster_link"/>
    <origin rpy="0 0 0" xyz="${joint_x} ${joint_y} 0.055325"/>
    <axis xyz="0 1 0"/>
  </joint>
  </xacro:macro>
  <xacro:caster prefix="fl" joint_x="0.15" joint_y="0.12"/>
  <xacro:caster prefix="fr" joint_x="0.15" joint_y="-0.12"/>
  <xacro:caster prefix="br" joint_x="-0.2" joint_y="0.12"/>
  <xacro:caster prefix="bl" joint_x="-0.2" joint_y="-0.12"/>

  <!-- Base is modeled as a big tub sitting on floor, with two wheels -->
  <gazebo reference="base_link">
    <kp>100000000.0</kp>
    <kd>10.0</kd>
    <mu1>0.1</mu1>
    <mu2>0.1</mu2>
    <fdir1>1 0 0</fdir1>
    <maxVel>10.0</maxVel>
    <minDepth>0.0005</minDepth>
  </gazebo>
  <gazebo reference="r_wheel_link">
    <kp>1000000.0</kp>
    <kd>100.0</kd>
    <mu1>100000000000000.0</mu1>
    <mu2>100000000000000.0</mu2>
    <!-- <fdir1>1 0 0</fdir1 -->
    <maxVel>1.0</maxVel>
    <minDepth>0.01</minDepth>
    <material>Gazebo/Black</material>
  </gazebo>
  <gazebo reference="l_wheel_link">
    <kp>1000000.0</kp>
    <kd>100.0</kd>
    <mu1>100000000000000.0</mu1>
    <mu2>100000000000000.0</mu2>
    <!-- fdir1>1 0 0</fdir1 -->
    <maxVel>1.0</maxVel>
    <minDepth>0.01</minDepth>
    <material>Gazebo/Black</material>
  </gazebo>
  <gazebo reference="l_wheel_joint">
    <implicitSpringDamper>1</implicitSpringDamper>
  </gazebo>
  <gazebo reference="r_wheel_joint">
    <implicitSpringDamper>1</implicitSpringDamper>
  </gazebo>
  <gazebo reference="forearm_roll_joint">
    <implicitSpringDamper>1</implicitSpringDamper>
  </gazebo>
  <gazebo reference="wrist_roll_joint">
    <implicitSpringDamper>1</implicitSpringDamper>
  </gazebo>

  <!-- Gripper is another fallacy of physics -->
  <gazebo reference="r_gripper_finger_link">
    <kp>1000000.0</kp>
    <kd>100.0</kd>
    <mu1>30.0</mu1>
    <mu2>30.0</mu2>
    <maxVel>1.0</maxVel>
    <minDepth>0.001</minDepth>
    <material>Gazebo/Grey</material>
  </gazebo>
  <gazebo reference="l_gripper_finger_link">
    <kp>1000000.0</kp>
    <kd>100.0</kd>
    <mu1>30.0</mu1>
    <mu2>30.0</mu2>
    <maxVel>1.0</maxVel>
    <minDepth>0.001</minDepth>
    <material>Gazebo/Grey</material>
  </gazebo>

  <!-- SICK TIM561 (25m Range) -->
  <gazebo reference="laser_link">
    <sensor type="ray" name="base_laser">
      <pose>0 0 0 0 0 0</pose>
      <visualize>false</visualize>
      <update_rate>15</update_rate>
      <ray>
        <scan>
          <horizontal>
            <samples>662</samples>
            <resolution>1</resolution>
            <min_angle>-1.91986</min_angle>
            <max_angle>1.91986</max_angle>
          </horizontal>
        </scan>
        <range>
          <min>0.05</min>
          <max>25.0</max>
          <resolution>0.01</resolution>
        </range>
        <noise>
          <!-- Noise parameters based on spec for SICK TIM561 (10m version) -->
          <type>gaussian</type>
          <mean>0.0</mean>
          <stddev>0.02</stddev>
        </noise>
      </ray>
      <plugin name="gazebo_ros_base_hokuyo_controller" filename="libgazebo_ros_laser.so">
        <topicName>/base_scan</topicName>
        <frameName>laser_link</frameName>
      </plugin>
    </sensor>
  </gazebo>

  <!-- Primesense Carmine 1.09 -->
  <gazebo reference="head_camera_rgb_frame">
    <sensor type="depth" name="camera">
      <always_on>true</always_on>
      <update_rate>15.0</update_rate>
      <camera>
        <horizontal_fov>1.047197</horizontal_fov>
        <image>
          <!-- openni_kinect plugin works only with BGR8 -->
          <format>B8G8R8</format>
          <width>640</width>
          <height>480</height>
        </image>
        <clip>
          <near>0.05</near>
          <far>50</far>
        </clip>
      </camera>
      <plugin name="head_camera_controller" filename="libgazebo_ros_openni_kinect.so">
        <baseline>0.1</baseline>
        <alwaysOn>true</alwaysOn>
        <updateRate>15.0</updateRate>
        <cameraName>head_camera</cameraName>
        <imageTopicName>/head_camera/rgb/image_raw</imageTopicName>
        <cameraInfoTopicName>/head_camera/rgb/camera_info</cameraInfoTopicName>
        <depthImageTopicName>/head_camera/depth_registered/image_raw</depthImageTopicName>
        <depthImageCameraInfoTopicName>/head_camera/depth_registered/camera_info</depthImageCameraInfoTopicName>
        <pointCloudTopicName>/head_camera/depth_registered/points</pointCloudTopicName>
        <frameName>head_camera_rgb_optical_frame</frameName>
        <pointCloudCutoff>0.35</pointCloudCutoff>
        <pointCloudCutoffMax>4.5</pointCloudCutoffMax>
        <CxPrime>0</CxPrime>
        <Cx>0</Cx>
        <Cy>0</Cy>
        <focalLength>0</focalLength>
        <hackBaseline>0</hackBaseline>
      </plugin>
    </sensor>
  </gazebo>

  <!-- Color the bellows -->
  <gazebo reference="bellows_link">
    <material>Gazebo/Black</material>
  </gazebo>
  <gazebo reference="bellows_link2">
    <material>Gazebo/Black</material>
  </gazebo>

  <!-- Color the estop -->
  <gazebo reference="estop_link">
    <material>Gazebo/Red</material>
  </gazebo>

  <!-- Load the plugin -->
  <gazebo>
    <plugin name="fetch_gazebo_plugin" filename="libfetch_gazebo_plugin.so"/>
  </gazebo>

</robot>
