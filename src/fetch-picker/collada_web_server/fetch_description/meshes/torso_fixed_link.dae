<?xml version="1.0" encoding="utf-8"?>
<COLLADA xmlns="http://www.collada.org/2005/11/COLLADASchema" version="1.4.1">
  <asset>
    <contributor>
      <author><PERSON></author>
      <authoring_tool>Blender 2.73.0 commit date:2015-01-20, commit time:18:16, hash:bbf09d9</authoring_tool>
    </contributor>
    <created>2015-01-31T15:49:27</created>
    <modified>2015-01-31T15:49:27</modified>
    <unit name="meter" meter="1"/>
    <up_axis>Z_UP</up_axis>
  </asset>
  <library_images>
    <image id="torso_fixed_uv" name="torso_fixed_uv">
      <init_from>torso_fixed_uv.png</init_from>
    </image>
  </library_images>
  <library_effects>
    <effect id="Material_001-effect">
      <profile_COMMON>
        <newparam sid="torso_fixed_uv-surface">
          <surface type="2D">
            <init_from>torso_fixed_uv</init_from>
          </surface>
        </newparam>
        <newparam sid="torso_fixed_uv-sampler">
          <sampler2D>
            <source>torso_fixed_uv-surface</source>
          </sampler2D>
        </newparam>
        <technique sid="common">
          <phong>
            <emission>
              <color sid="emission">0 0 0 1</color>
            </emission>
            <ambient>
              <color sid="ambient">1 1 1 1</color>
            </ambient>
            <diffuse>
              <texture texture="torso_fixed_uv-sampler" texcoord="UVMap"/>
            </diffuse>
            <specular>
              <color sid="specular">0.5 0.5 0.5 1</color>
            </specular>
            <shininess>
              <float sid="shininess">50</float>
            </shininess>
            <index_of_refraction>
              <float sid="index_of_refraction">1</float>
            </index_of_refraction>
          </phong>
        </technique>
      </profile_COMMON>
    </effect>
  </library_effects>
  <library_materials>
    <material id="Material_001-material" name="Material_001">
      <instance_effect url="#Material_001-effect"/>
    </material>
  </library_materials>
  <library_geometries>
    <geometry id="torso_fixed_link-mesh" name="torso fixed link">
      <mesh>
        <source id="torso_fixed_link-mesh-positions">
          <float_array id="torso_fixed_link-mesh-positions-array" count="180">-0.1483161 0.1233052 -0.01856917 -0.1593292 0.09951865 0.4433058 -0.1483157 0.1233052 0.4433058 -0.1174655 -0.15535 -0.01856917 -0.1259489 -0.1500836 -0.01856917 -0.03506553 -0.15535 -0.01856923 -0.1274321 -0.1528342 -0.01856917 -0.1649452 -0.0738821 -0.01856917 -0.1649452 0.07388192 -0.01856917 -0.03506547 0.152225 -0.01856923 -0.03356552 -0.15535 -0.01816731 -0.1350103 0.1458898 0.4433058 -0.1350106 0.1458898 -0.01856917 -0.03506547 0.15535 -0.01856923 -0.1259489 0.1500836 -0.01856917 -0.1174654 0.15535 -0.01856917 -0.1174651 -0.15535 0.4433058 -0.1274317 -0.1528342 0.4433058 -0.1593296 -0.09951871 -0.01856917 -0.1682877 -0.0723685 -0.01856917 -0.1593296 0.09951865 -0.01856917 -0.1682877 0.07236838 -0.01856917 -0.03246736 0.152225 -0.01706928 -0.03246742 -0.15535 -0.01706928 -0.1483161 -0.1233053 -0.01856917 -0.1350103 -0.1458898 0.4433058 -0.1483157 -0.1233053 0.4433058 -0.1274316 0.1528342 0.4433058 -0.1174651 0.15535 0.4433058 -0.032467 0.152225 0.4418057 -0.03206515 -0.15535 0.4403057 -0.03206509 0.152225 0.4403057 -0.156432 0.09834724 0.4433058 -0.1593292 -0.09951871 0.4433058 -0.1679437 -0.0747621 -0.01856917 -0.1679434 -0.0747621 0.4433058 -0.1455486 -0.1218538 -0.01856917 -0.1679437 0.07476192 -0.01856917 -0.1455486 0.1218538 -0.01856917 -0.03356546 0.152225 -0.01816731 -0.127432 0.1528342 -0.01856917 -0.03506511 -0.15535 0.4433057 -0.03506511 0.152225 0.4433057 -0.156432 -0.0983473 0.4433058 -0.1350107 -0.1458898 -0.01856917 -0.1679434 0.07476192 0.4433058 -0.03206545 0.152225 -0.01556926 -0.03206551 -0.15535 -0.01556926 -0.03356516 -0.15535 0.4429038 -0.0335651 0.152225 0.4429038 -0.03246706 -0.15535 0.4418057 -0.1682873 0.07236838 0.4433058 -0.1682873 -0.0723685 0.4433058 -0.03506511 0.15535 0.4433057 -0.0335651 0.15535 0.4429038 -0.032467 0.15535 0.4418057 -0.03206509 0.15535 0.4403057 -0.03246736 0.15535 -0.01706928 -0.03206545 0.15535 -0.01556926 -0.03356546 0.15535 -0.01816731</float_array>
          <technique_common>
            <accessor source="#torso_fixed_link-mesh-positions-array" count="60" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="torso_fixed_link-mesh-normals">
          <float_array id="torso_fixed_link-mesh-normals-array" count="348">-0.9074496 0.4201611 7.30786e-7 -7.91172e-7 -2.13417e-7 -1 -8.08456e-7 -2.4126e-7 -1 -7.74431e-7 0 -1 0.2588183 0 -0.965926 -9.93373e-6 5.85236e-6 1 -0.8615936 0.5075988 6.94727e-7 -0.8615936 0.5075988 6.94925e-7 -7.91172e-7 2.13417e-7 -1 -0.2447427 -0.9695881 1.97313e-7 -0.2447423 -0.9695882 1.89503e-7 -1.16419e-6 0 -1 -1.43998e-6 3.53503e-7 -1 -1.16419e-6 0 -1 -0.9074496 0.4201611 7.31911e-7 0.7071056 -1.19901e-7 -0.707108 -0.8615934 -0.5075991 6.94729e-7 -1.13374e-6 1.23727e-6 1 -1.66194e-7 6.5841e-7 1 0.9659267 -1.40389e-7 0.258816 1.65617e-7 4.09607e-7 1 -1.66196e-7 -6.58412e-7 1 -1.13374e-6 -1.23727e-6 1 -9.93368e-6 -5.85233e-6 1 -0.9074496 -0.4201607 7.30787e-7 -0.9074496 -0.4201608 7.31911e-7 -1.47341e-6 -2.11724e-7 -1 -0.9444596 -0.3286275 7.61762e-7 -0.9444596 -0.3286275 7.62037e-7 -8.92195e-7 0 -1 -7.76536e-7 0 -1 -7.79582e-7 0 -1 -0.9444598 0.3286271 7.60834e-7 -1.47341e-6 2.11723e-7 -1 -8.51522e-7 0 -1 -1.06613e-6 1.57442e-7 -1 -8.92192e-7 0 -1 -7.76535e-7 0 -1 -7.79582e-7 0 -1 0.7071032 0 -0.7071104 0.2588195 0 -0.9659256 -0.244741 0.9695885 1.89502e-7 -0.2447413 0.9695884 1.97398e-7 -8.08456e-7 2.41259e-7 -1 -9.08815e-7 1.87146e-7 -1 1.08504e-6 0 1 1.65617e-7 -4.09608e-7 1 7.6481e-7 0 1 7.6481e-7 0 1 -9.08815e-7 -1.87147e-7 -1 -8.51521e-7 0 -1 -0.8615934 -0.5075991 6.94729e-7 -1.43997e-6 -3.53498e-7 -1 -1.06614e-6 -1.57444e-7 -1 -0.6755866 -0.7372807 5.44813e-7 -0.6755866 -0.7372807 5.449e-7 -0.9444598 0.3286271 7.61762e-7 0.9659262 -1.40389e-7 -0.2588177 0.965925 -1.63757e-7 -0.2588222 0.2588139 0 0.9659272 0.2588127 0 0.9659275 0.707102 0 0.7071116 0.9659255 -1.63787e-7 0.2588205 0.7071043 -1.199e-7 0.7071092 -7.74431e-7 0 -1 -7.74431e-7 0 -1 -1 0 8.06558e-7 -1 0 8.06558e-7 -1.11452e-6 0 -1 -1.11452e-6 0 -1 1.08504e-6 0 1 1.08504e-6 0 1 0.258814 0 0.9659272 0.258814 0 0.9659272 0.707102 0 0.7071115 0.707102 0 0.7071115 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0.9659266 0 0.258816 0.9659266 0 0.258816 -0.9898328 -0.1422357 7.98358e-7 -0.9898328 -0.1422357 7.98358e-7 0.7071031 0 -0.7071104 0.7071031 0 -0.7071104 0.2588195 0 -0.9659257 0.2588195 0 -0.9659257 0.9659262 0 -0.2588177 0.9659262 0 -0.2588177 1 0 -7.84487e-7 1 0 -7.84487e-7 -0.6755874 0.73728 5.44813e-7 -0.6755874 0.73728 5.449e-7 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 -0.9898328 0.1422352 7.98358e-7 -0.9898328 0.1422352 7.98379e-7 1 -1.45342e-7 -7.84487e-7 1 -1.45342e-7 -7.84487e-7 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0</float_array>
          <technique_common>
            <accessor source="#torso_fixed_link-mesh-normals-array" count="116" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="torso_fixed_link-mesh-map-0">
          <float_array id="torso_fixed_link-mesh-map-0-array" count="696">0.4119448 0 0.4413453 0.600169 0.4119448 0.600169 0.105062 0.5962706 0.1155418 0.6031138 0.003271579 0.5962706 0.1173738 0.5995396 0.1155418 0.6031138 0.105062 0.5962706 0.1637146 0.7021315 0.1637146 0.8941389 0.003271579 0.5962706 0.003271579 0.9959393 0.00140196 0.5962706 0.003271579 0.5962706 0.1789047 0.9274517 0.2089325 0.9877072 0.1925036 0.9583604 0.4119448 0.600169 0.3840299 0.600169 0.4119448 0 0.3840299 0.600169 0.3840299 0 0.4119448 0 0.003271579 1 0.1155418 0.9931567 0.105062 0.9999999 0.9902218 0.6001688 0.977765 0 0.9902218 0 0.977765 0.6001688 0.977765 0 0.9902218 0.6001688 0.1637146 0.7021315 0.1567775 0.6688188 0.1678436 0.7040983 0.1267359 0.9877071 0.1567776 0.9274516 0.1431724 0.9583603 0.1678436 0.8921721 0.1567776 0.9274516 0.1637146 0.8941389 0.4119448 0 0.4413453 0 0.4413453 0.600169 0.00140196 0.5962706 0 0.9959393 0 0.5962706 0.7167591 0 0.7446739 0.600169 0.7167591 0.600169 0.1789047 0.9274517 0.2182903 0.996731 0.2089325 0.9877072 0.2305964 1 0.2182903 0.996731 0.1789047 0.9274517 0.003862559 0 0.3840299 0.001949071 0.003862559 0.001949071 0.2305964 1 0.1789047 0.9274517 0.182482 0.9259296 0.2305964 0.5962706 0.1789047 0.6688188 0.2182902 0.5995397 0.1789047 0.6688188 0.2089325 0.6085634 0.2182902 0.5995397 0.2089325 0.6085634 0.1789047 0.6688188 0.1925036 0.6379102 0.7167591 0.600169 0.6873586 0.600169 0.7167591 0 0.7167591 0 0.6873586 0.600169 0.6873586 0 0.1567775 0.6688188 0.1674188 0.700988 0.1678436 0.7040983 0.656759 0.600169 0.656759 0 0.6873586 0.600169 0.6873586 0.600169 0.656759 0 0.6873586 0 0.1397536 0.6397961 0.1567775 0.6688188 0.1637146 0.7021315 0.1397536 0.6397961 0.1637146 0.7021315 0.003271579 0.5962706 0.1155418 0.6031138 0.1397536 0.6397961 0.003271579 0.5962706 0.4413453 0 0.4719449 0 0.4413453 0.600169 0.1567776 0.9274516 0.1678436 0.8921721 0.1674188 0.8952823 0.1155418 0.9931567 0.1397536 0.9564744 0.1267359 0.9877071 0.1397536 0.9564744 0.1567776 0.9274516 0.1267359 0.9877071 0.1567776 0.9274516 0.1397536 0.9564744 0.1637146 0.8941389 0.1637146 0.8941389 0.1397536 0.9564744 0.003271579 1 0.1155418 0.9931567 0.003271579 1 0.1397536 0.9564744 0 0.9959393 0.00140196 0.5962706 0.00140196 0.9959393 0.00140196 0.5962706 0.003271579 0.9959393 0.00140196 0.9959393 0.7669089 0 0.7669091 0.600169 0.7544523 0.600169 0.7544523 0.600169 0.7544521 0 0.7669089 0 0.1155418 0.9931567 0.1173739 0.9967309 0.105062 0.9999999 0.1155418 0.9931567 0.1267359 0.9877071 0.1173739 0.9967309 0.2305964 0.5962706 0.3323398 0.5962706 0.3323399 0.9959393 0.1789047 0.6688188 0.2305964 0.5962706 0.182482 0.670341 0.2305964 1 0.182482 0.670341 0.2305964 0.5962706 0.182482 0.9259296 0.182482 0.670341 0.2305964 1 0.1155418 0.6031138 0.1173738 0.5995396 0.1267359 0.6085634 0.1397536 0.6397961 0.1155418 0.6031138 0.1267359 0.6085634 0.7446739 0.600169 0.7167591 0 0.7446739 0 0.1567775 0.6688188 0.1267359 0.6085634 0.1431724 0.6379101 0.1397536 0.6397961 0.1267359 0.6085634 0.1567775 0.6688188 0.9902218 0 1 0.6001688 0.9902218 0.6001688 1 0 1 0.6001688 0.9902218 0 0.4719449 0 0.471945 0.600169 0.4413453 0.600169 0.003862559 0.5943215 0.3840299 0.5943215 0.003862559 0.5962706 0.003862559 0.5962706 0.3840299 0.5943215 0.3840299 0.5962706 0.3323399 0.9959393 0.3342143 0.5962706 0.3342145 0.9959393 0.3323398 0.5962706 0.3342143 0.5962706 0.3323399 0.9959393 0.3342143 0.5962706 0.3356317 0.9959393 0.3342145 0.9959393 0.3840299 0.001949071 0.003862559 0 0.3840299 0 0.3342143 0.5962706 0.3356316 0.5962706 0.3356317 0.9959393 0.1637146 0.8941389 0.003271579 1 0.003271579 0.9959393 0.1637146 0.8941389 0.003271579 0.9959393 0.003271579 0.5962706 0.4749035 0.600169 0.4749034 0 0.6538005 0.600169 0.6538005 0.600169 0.4749034 0 0.6538004 0 0.1637146 0.7021315 0.1678436 0.7040983 0.1678436 0.8921721 0.1678436 0.8921721 0.1637146 0.8941389 0.1637146 0.7021315 0.2305964 1 0.3323399 0.9959393 0.3323398 1 0.2305964 0.5962706 0.3323399 0.9959393 0.2305964 1 0.3323399 0.9959393 0.3342145 1 0.3323398 1 0.3342145 1 0.3323399 0.9959393 0.3342145 0.9959393 0.3342145 1 0.3356317 0.9959393 0.3356317 1 0.3342145 1 0.3342145 0.9959393 0.3356317 0.9959393 0.8686335 0.6001689 0.7669091 0.600169 0.8723368 0.003898203 0.7669089 0 0.8723368 0.003898203 0.7669091 0.600169 0.8718408 0.5982198 0.8723368 0.003898203 0.8723371 0.5962706 0.8686335 0.6001689 0.8723368 0.003898203 0.8704853 0.5996466 0.8718408 0.5982198 0.8704853 0.5996466 0.8723368 0.003898203 0.8723368 0.003898203 0.7669089 0 0.8686333 0 0.870485 5.22216e-4 0.8718407 0.001949071 0.8723368 0.003898203 0.870485 5.22216e-4 0.8723368 0.003898203 0.8686333 0 0.003862559 0 0 0.001949071 0 0 0.003862559 0 0.003862559 0.001949071 0 0.001949071 0.6538005 0.600169 0.656759 0 0.656759 0.600169 0.6538005 0.600169 0.6538004 0 0.656759 0 0 1 0.00140196 0.9959393 0.00140196 1 0 0.9959393 0.00140196 0.9959393 0 1 0.00140196 1 0.00140196 0.9959393 0.003271579 1 0.003271579 1 0.00140196 0.9959393 0.003271579 0.9959393 0 0.5943215 0.003862559 0.5943215 0 0.5962706 0 0.5962706 0.003862559 0.5943215 0.003862559 0.5962706 0 0.001949071 0.003862559 0.5943215 0 0.5943215 0.003862559 0.5943215 0 0.001949071 0.003862559 0.001949071 0.7446739 0 0.7544523 0.600169 0.7446741 0.600169 0.7544521 0 0.7544523 0.600169 0.7446739 0 0.1678436 0.7040984 0.1682683 0.8952824 0.1678436 0.8921721 0.182482 0.670341 0.1682683 0.8952824 0.1789047 0.6688188 0.1682683 0.7009881 0.1682683 0.8952824 0.1678436 0.7040984 0.182482 0.9259296 0.1789047 0.9274517 0.1682683 0.8952824 0.1789047 0.6688188 0.1682683 0.8952824 0.1682683 0.7009881 0.1682683 0.8952824 0.182482 0.670341 0.182482 0.9259296 0.4749034 0 0.4749035 0.600169 0.471945 0.600169 0.4719449 0 0.4749034 0 0.471945 0.600169 0.003862559 0.001949071 0.3840299 0.5943215 0.003862559 0.5943215 0.3840299 0.001949071 0.3840299 0.5943215 0.003862559 0.001949071 0.8741888 0.5996466 0.8728333 0.5982197 0.8728333 0.001949071 0.977765 0.6001688 0.8760406 0.6001688 0.8728333 0.001949071 0.8741888 0.5996466 0.8728333 0.001949071 0.8760406 0.6001688 0.977765 0 0.977765 0.6001688 0.8728333 0.001949071 0.8723371 0.5962706 0.8728333 0.001949071 0.8728333 0.5982197 0.8760406 0 0.977765 0 0.8728333 0.001949071 0.8728333 0.001949071 0.8723371 0.5962706 0.8723371 0.003898203 0.8741888 5.22332e-4 0.8760406 0 0.8728333 0.001949071</float_array>
          <technique_common>
            <accessor source="#torso_fixed_link-mesh-map-0-array" count="348" stride="2">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <vertices id="torso_fixed_link-mesh-vertices">
          <input semantic="POSITION" source="#torso_fixed_link-mesh-positions"/>
        </vertices>
        <polylist material="Material_001-material" count="116">
          <input semantic="VERTEX" source="#torso_fixed_link-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#torso_fixed_link-mesh-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#torso_fixed_link-mesh-map-0" offset="2" set="0"/>
          <vcount>3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 </vcount>
          <p>0 0 0 1 0 1 2 0 2 3 1 3 4 1 4 5 1 5 6 2 6 4 2 7 3 2 8 7 3 9 8 3 10 5 3 11 9 4 12 10 4 13 5 4 14 1 5 15 11 5 16 2 5 17 2 6 18 11 6 19 0 6 20 11 7 21 12 7 22 0 7 23 13 8 24 14 8 25 15 8 26 6 9 27 16 9 28 17 9 29 3 10 30 16 10 31 6 10 32 7 11 33 18 11 34 19 11 35 12 12 36 20 12 37 0 12 38 21 13 39 20 13 40 8 13 41 0 14 42 20 14 43 1 14 44 10 15 45 22 15 46 23 15 47 24 16 48 25 16 49 26 16 50 1 17 51 27 17 52 11 17 53 28 18 54 27 18 55 1 18 56 29 19 57 30 19 58 31 19 59 28 20 60 1 20 61 32 20 62 16 21 63 33 21 64 17 21 65 33 22 66 25 22 67 17 22 68 25 23 69 33 23 70 26 23 71 26 24 72 33 24 73 24 24 74 24 25 75 33 25 76 18 25 77 18 26 78 34 26 79 19 26 80 35 27 81 34 27 82 33 27 83 33 28 84 34 28 85 18 28 86 36 29 87 18 29 88 7 29 89 36 30 90 7 30 91 5 30 92 4 31 93 36 31 94 5 31 95 20 32 96 37 32 97 1 32 98 20 33 99 21 33 100 37 33 101 14 34 102 38 34 103 12 34 104 38 35 105 20 35 106 12 35 107 20 36 108 38 36 109 8 36 110 8 37 111 38 37 112 13 37 113 14 38 114 13 38 115 38 38 116 22 39 117 10 39 118 39 39 119 10 40 120 9 40 121 39 40 122 28 41 123 15 41 124 40 41 125 40 42 126 27 42 127 28 42 128 14 43 129 40 43 130 15 43 131 14 44 132 12 44 133 40 44 134 16 45 135 41 45 136 42 45 137 33 46 138 16 46 139 43 46 140 28 47 141 43 47 142 16 47 143 32 48 144 43 48 145 28 48 146 4 49 147 6 49 148 44 49 149 36 50 150 4 50 151 44 50 152 25 51 153 24 51 154 44 51 155 18 52 156 44 52 157 24 52 158 36 53 159 44 53 160 18 53 161 17 54 162 44 54 163 6 54 164 25 55 165 44 55 166 17 55 167 37 56 168 45 56 169 1 56 170 46 57 171 47 57 172 22 57 173 22 58 174 47 58 175 23 58 176 42 59 177 48 59 178 49 59 179 41 60 180 48 60 181 42 60 182 48 61 183 29 61 184 49 61 185 30 62 186 29 62 187 50 62 188 48 63 189 50 63 190 29 63 191 8 64 192 13 64 193 9 64 194 8 65 195 9 65 196 5 65 197 51 66 198 21 66 199 52 66 200 52 67 201 21 67 202 19 67 203 7 68 204 19 68 205 21 68 206 21 69 207 8 69 208 7 69 209 28 70 210 42 70 211 53 70 212 16 71 213 42 71 214 28 71 215 42 72 216 54 72 217 53 72 218 54 73 219 42 73 220 49 73 221 54 74 222 29 74 223 55 74 224 54 75 225 49 75 226 29 75 227 13 76 228 15 76 229 56 76 230 28 77 231 56 77 232 15 77 233 57 78 234 56 78 235 58 78 236 13 79 237 56 79 238 59 79 239 57 80 240 59 80 241 56 80 242 56 81 243 28 81 244 53 81 245 54 82 246 55 82 247 56 82 248 54 83 249 56 83 250 53 83 251 29 84 252 56 84 253 55 84 254 29 85 255 31 85 256 56 85 257 52 86 258 34 86 259 35 86 260 52 87 261 19 87 262 34 87 263 57 88 264 39 88 265 59 88 266 22 89 267 39 89 268 57 89 269 59 90 270 39 90 271 13 90 272 13 91 273 39 91 274 9 91 275 58 92 276 46 92 277 57 92 278 57 93 279 46 93 280 22 93 281 56 94 282 46 94 283 58 94 284 46 95 285 56 95 286 31 95 287 11 96 288 40 96 289 12 96 290 27 97 291 40 97 292 11 97 293 52 98 294 45 98 295 51 98 296 43 99 297 45 99 298 33 99 299 35 100 300 45 100 301 52 100 302 32 101 303 1 101 304 45 101 305 33 102 306 45 102 307 35 102 308 45 103 309 43 103 310 32 103 311 21 104 312 51 104 313 45 104 314 37 105 315 21 105 316 45 105 317 31 106 318 47 106 319 46 106 320 30 107 321 47 107 322 31 107 323 10 108 324 23 108 325 50 108 326 3 109 327 5 109 328 50 109 329 10 110 330 50 110 331 5 110 332 16 111 333 3 111 334 50 111 335 47 112 336 50 112 337 23 112 338 41 113 339 16 113 340 50 113 341 50 114 342 47 114 343 30 114 344 48 115 345 41 115 346 50 115 347</p>
        </polylist>
      </mesh>
    </geometry>
  </library_geometries>
  <library_controllers/>
  <library_visual_scenes>
    <visual_scene id="Scene" name="Scene">
      <node id="Camera" name="Camera" type="NODE">
        <matrix sid="transform">0.6858805 -0.3173701 0.6548619 7.481132 0.7276338 0.3124686 -0.6106656 -6.50764 -0.01081678 0.8953432 0.4452454 5.343665 0 0 0 1</matrix>
        <instance_camera url="#Camera-camera"/>
      </node>
      <node id="Lamp" name="Lamp" type="NODE">
        <matrix sid="transform">-0.2908646 -0.7711008 0.5663932 4.076245 0.9551712 -0.1998834 0.2183912 1.005454 -0.05518906 0.6045247 0.7946723 5.903862 0 0 0 1</matrix>
        <instance_light url="#Lamp-light"/>
      </node>
      <node id="torso_fixed_link" name="torso_fixed_link" type="NODE">
        <matrix sid="transform">1 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry url="#torso_fixed_link-mesh">
          <bind_material>
            <technique_common>
              <instance_material symbol="Material_001-material" target="#Material_001-material">
                <bind_vertex_input semantic="UVMap" input_semantic="TEXCOORD" input_set="0"/>
              </instance_material>
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
    </visual_scene>
  </library_visual_scenes>
  <scene>
    <instance_visual_scene url="#Scene"/>
  </scene>
</COLLADA>
