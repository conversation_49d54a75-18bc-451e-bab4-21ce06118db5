<robot name="freight">
  <link name="base_link">
    <inertial>
      <origin rpy="0 0 0" xyz="-0.0036 0.0 0.0014" />
      <mass value="70.1294" />
      <inertia ixx="1.225" ixy="0.0099" ixz="0.0062" iyy="1.2853" iyz="-0.0034" izz="0.987" />
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0" />
      <geometry>
        <mesh filename="package://fetch_description/meshes/base_link.dae" />
      </geometry>
      <material name="">
        <color rgba="0.356 0.361 0.376 1" />
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0" />
      <geometry>
        <mesh filename="package://fetch_description/meshes/base_link_collision.STL" />
      </geometry>
    </collision>
  </link>
  <link name="r_wheel_link">
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0" />
      <mass value="4.3542" />
      <inertia ixx="0.0045" ixy="0" ixz="0" iyy="0.005" iyz="0" izz="0.0045" />
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0" />
      <geometry>
        <mesh filename="package://fetch_description/meshes/r_wheel_link.STL" />
      </geometry>
      <material name="">
        <color rgba="0.086 0.506 0.767 1" />
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0" />
      <geometry>
        <mesh filename="package://fetch_description/meshes/r_wheel_link_collision.STL" />
      </geometry>
    </collision>
  </link>
  <joint name="r_wheel_joint" type="continuous">
    <origin rpy="-6.123E-17 0 0" xyz="0.0012914 -0.18738 0.055325" />
    <parent link="base_link" />
    <child link="r_wheel_link" />
    <axis xyz="0 1 0" />
  <limit effort="8.85" velocity="34.8" /></joint>
  <link name="l_wheel_link">
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0" />
      <mass value="4.3542" />
      <inertia ixx="0.0045" ixy="0" ixz="0" iyy="0.005" iyz="0" izz="0.0045" />
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0" />
      <geometry>
        <mesh filename="package://fetch_description/meshes/l_wheel_link.STL" />
      </geometry>
      <material name="">
        <color rgba="0.086 0.506 0.767 1" />
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0" />
      <geometry>
        <mesh filename="package://fetch_description/meshes/l_wheel_link_collision.STL" />
      </geometry>
    </collision>
  </link>
  <joint name="l_wheel_joint" type="continuous">
    <origin rpy="-6.123E-17 0 0" xyz="0.0012914 0.18738 0.055325" />
    <parent link="base_link" />
    <child link="l_wheel_link" />
    <axis xyz="0 1 0" />
  <limit effort="8.85" velocity="34.8" /></joint>
  <link name="estop_link">
    <inertial>
      <origin rpy="0 0 0" xyz="0.002434512737072 -0.00330608315239905 0.00665139196650039" />
      <mass value="0.00196130439134723" />
      <inertia ixx="3.02810026604417E-07" ixy="-1.5862023118056E-08" ixz="3.16561396557437E-08" iyy="2.93322917127605E-07" iyz="-4.28833522751273E-08" izz="2.28513272627183E-07" />
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0" />
      <geometry>
        <mesh filename="package://fetch_description/meshes/estop_link.STL" />
      </geometry>
      <material name="">
        <color rgba="0.8 0 0 1" />
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0" />
      <geometry>
        <mesh filename="package://fetch_description/meshes/estop_link.STL" />
      </geometry>
    </collision>
  </link>
  <joint name="estop_joint" type="fixed">
    <origin rpy="1.5708 0 0" xyz="-0.12465 0.23892 0.31127" />
    <parent link="base_link" />
    <child link="estop_link" />
    <axis xyz="0 0 0" />
  </joint>
  <link name="laser_link">
    <inertial>
      <origin rpy="0 0 0" xyz="-0.0306228970175641 0.0007091682908278 0.0551974119471302" />
      <mass value="0.00833634573995571" />
      <inertia ixx="1.01866461240801E-06" ixy="-5.88447626567756E-08" ixz="7.83857244757914E-08" iyy="5.10039589974707E-07" iyz="-7.12664289617235E-09" izz="1.28270671527309E-06" />
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0" />
      <geometry>
        <mesh filename="package://fetch_description/meshes/laser_link.STL" />
      </geometry>
      <material name="">
        <color rgba="0.792156862745098 0.819607843137255 0.933333333333333 1" />
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0" />
      <geometry>
        <mesh filename="package://fetch_description/meshes/laser_link.STL" />
      </geometry>
    </collision>
  </link>
  <joint name="laser_joint" type="fixed">
    <origin rpy="3.14159265359 0 0" xyz="0.235 0 0.2878" />
    <parent link="base_link" />
    <child link="laser_link" />
    <axis xyz="0 0 0" />
  </joint>
  <link name="base_camera_link" />
  <joint name="base_camera_joint" type="fixed">
    <origin rpy="0 -0.17453292519943 0" xyz="0.2645 0 0.198" />
    <parent link="base_link" />
    <child link="base_camera_link" />
  </joint>
  <link name="base_camera_optical_frame" />
  <joint name="base_camera_optical_joint" type="fixed">
    <origin rpy="1.5707963267966 0 1.5707963267966" xyz="0 0 0" />
    <parent link="base_camera_link" />
    <child link="base_camera_optical_frame" />
  </joint>
</robot>
