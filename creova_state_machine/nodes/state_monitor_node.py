#!/usr/bin/env python3
"""
State Monitor Node - Publishes current system state for physical AI team
"""

import rclpy
from rclpy.node import Node
from std_msgs.msg import String
import json
from datetime import datetime

class StateMonitorNode(Node):
    def __init__(self):
        super().__init__('state_monitor')
        
        # Publisher for physical AI team
        self.physical_ai_status_pub = self.create_publisher(String, '/physical_ai/system_status', 10)
        
        # Subscriber for state changes from orchestration
        self.state_change_sub = self.create_subscription(
            String, '/state_changes', self.handle_state_change, 10)
        
        # Subscriber for manipulation commands to track robot activity
        self.manipulation_command_sub = self.create_subscription(
            String, '/manipulation/command', self.handle_manipulation_command, 10)
        
        # Current system state
        self.current_state = {
            'system_status': 'IDLE',
            'current_task': None,
            'task_queue_length': 0,
            'robot_busy': False,
            'navigation_busy': False,
            'last_update': datetime.now().isoformat(),
            'recent_activities': []
        }
        
        # Timer to publish status periodically
        self.status_timer = self.create_timer(2.0, self.publish_status)
        
        self.get_logger().info('State Monitor Node initialized')
    
    def handle_state_change(self, msg):
        """Handle state changes from orchestration."""
        try:
            state_data = json.loads(msg.data)
            self.get_logger().info(f"Received state change: {state_data}")
            
            # Update current state based on the change
            self.update_system_state(state_data)
            
            # Add to recent activities (keep last 10)
            activity = {
                'timestamp': state_data.get('timestamp', datetime.now().isoformat()),
                'entity': state_data.get('entity'),
                'transition': f"{state_data.get('from_state')} -> {state_data.get('to_state')}",
                'reason': state_data.get('reason', '')
            }
            
            self.current_state['recent_activities'].append(activity)
            if len(self.current_state['recent_activities']) > 10:
                self.current_state['recent_activities'].pop(0)
            
            self.current_state['last_update'] = datetime.now().isoformat()
            
        except json.JSONDecodeError:
            self.get_logger().error(f"Invalid JSON in state change: {msg.data}")
        except Exception as e:
            self.get_logger().error(f"Error processing state change: {e}")
    
    def handle_manipulation_command(self, msg):
        """Handle manipulation commands to track robot activity."""
        try:
            command_data = json.loads(msg.data)
            self.get_logger().info(f"Manipulation command: {command_data}")
            
            # Update robot busy status
            if command_data.get('command') == 'pick':
                self.current_state['robot_busy'] = True
                self.current_state['last_update'] = datetime.now().isoformat()
                
        except json.JSONDecodeError:
            self.get_logger().error(f"Invalid JSON in manipulation command: {msg.data}")
        except Exception as e:
            self.get_logger().error(f"Error processing manipulation command: {e}")
    
    def update_system_state(self, state_data):
        """Update the current system state based on state changes."""
        entity = state_data.get('entity')
        from_state = state_data.get('from_state')
        to_state = state_data.get('to_state')
        reason = state_data.get('reason', '')
        
        if entity == 'task':
            # Extract task ID from reason if available
            task_id = None
            if 'Task' in reason:
                try:
                    task_id = reason.split('Task ')[1].split(' ')[0]
                except:
                    pass
            
            # Update system status based on task state
            if to_state == 'PENDING':
                self.current_state['system_status'] = 'TASK_QUEUED'
                self.current_state['task_queue_length'] += 1
                if task_id:
                    self.current_state['current_task'] = {
                        'id': task_id,
                        'status': 'PENDING',
                        'started_at': datetime.now().isoformat()
                    }
            
            elif to_state == 'ACTIVE':
                self.current_state['system_status'] = 'SEARCHING_OBJECT'
                if task_id and self.current_state['current_task']:
                    self.current_state['current_task']['status'] = 'ACTIVE'
            
            elif to_state == 'PICKING':
                self.current_state['system_status'] = 'PICKING_OBJECT'
                self.current_state['robot_busy'] = True
                if task_id and self.current_state['current_task']:
                    self.current_state['current_task']['status'] = 'PICKING'
            
            elif to_state == 'WAITING_FOR_DELIVERY':
                self.current_state['system_status'] = 'READY_FOR_DELIVERY'
                self.current_state['robot_busy'] = False
                if task_id and self.current_state['current_task']:
                    self.current_state['current_task']['status'] = 'WAITING_FOR_DELIVERY'
            
            elif to_state == 'DELIVERING':
                self.current_state['system_status'] = 'DELIVERING'
                self.current_state['navigation_busy'] = True
                if task_id and self.current_state['current_task']:
                    self.current_state['current_task']['status'] = 'DELIVERING'
            
            elif to_state == 'COMPLETE':
                self.current_state['system_status'] = 'IDLE'
                self.current_state['navigation_busy'] = False
                self.current_state['robot_busy'] = False
                self.current_state['task_queue_length'] = max(0, self.current_state['task_queue_length'] - 1)
                if task_id and self.current_state['current_task']:
                    self.current_state['current_task']['status'] = 'COMPLETE'
                    self.current_state['current_task']['completed_at'] = datetime.now().isoformat()
                
                # Clear current task if queue is empty
                if self.current_state['task_queue_length'] == 0:
                    self.current_state['current_task'] = None
            
            elif to_state == 'FAILED':
                self.current_state['system_status'] = 'ERROR'
                self.current_state['navigation_busy'] = False
                self.current_state['robot_busy'] = False
                self.current_state['task_queue_length'] = max(0, self.current_state['task_queue_length'] - 1)
                if task_id and self.current_state['current_task']:
                    self.current_state['current_task']['status'] = 'FAILED'
                    self.current_state['current_task']['failed_at'] = datetime.now().isoformat()
                    self.current_state['current_task']['error_reason'] = reason
    
    def publish_status(self):
        """Publish current system status for physical AI team."""
        msg = String()
        msg.data = json.dumps(self.current_state, indent=2)
        self.physical_ai_status_pub.publish(msg)
        
        # Log summary periodically
        if hasattr(self, '_last_log_time'):
            if (datetime.now() - self._last_log_time).seconds >= 10:
                self._log_status_summary()
                self._last_log_time = datetime.now()
        else:
            self._last_log_time = datetime.now()
    
    def _log_status_summary(self):
        """Log a summary of current status."""
        status = self.current_state['system_status']
        queue_len = self.current_state['task_queue_length']
        current_task = self.current_state['current_task']
        
        summary = f"System: {status}, Queue: {queue_len} tasks"
        if current_task:
            summary += f", Current Task: {current_task['id']} ({current_task['status']})"
        
        self.get_logger().info(f"Status Summary: {summary}")

def main(args=None):
    rclpy.init(args=args)
    node = StateMonitorNode()
    try:
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    finally:
        node.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
