
FROM osrf/ros:humble-desktop-full

# Add ubuntu user with same UID and GID as your host system, if it doesn't already exist
# Since Ubuntu 24.04, a non-root user is created by default with the name vscode and UID=1000
ARG USERNAME=ubuntu
ARG USER_UID=1000
ARG USER_GID=$USER_UID
RUN if ! id -u $USER_UID >/dev/null 2>&1; then \
        groupadd --gid $USER_GID $USERNAME && \
        useradd -s /bin/bash --uid $USER_UID --gid $USER_GID -m $USERNAME; \
    fi
# Add sudo support for the non-root user
RUN apt-get update && \
    apt-get install -y sudo && \
    echo "$USERNAME ALL=(root) NOPASSWD:ALL" > /etc/sudoers.d/$USERNAME && \
    chmod 0440 /etc/sudoers.d/$USERNAME

# Switch from root to user
USER $USERNAME

# Add user to video group to allow access to webcam
RUN sudo usermod --append --groups video $USERNAME

# Update all packages
RUN sudo apt update && sudo apt upgrade -y

# Install Git and other dependencies
RUN sudo apt install -y git python3-pip python3-colcon-common-extensions python3-rosdep python3-vcstool

# Rosdep update
RUN rosdep update

# Source the ROS 2 setup file
RUN echo "source /opt/ros/${ROS_DISTRO}/setup.bash" >> ~/.bashrc
RUN echo "source /fetch_ws/install/setup.bash" >> ~/.bashrc

# Set up colcon workspace defaults
RUN echo 'export COLCON_WS=/fetch_ws' >> ~/.bashrc
RUN echo 'alias cb="cd $COLCON_WS && colcon build --symlink-install"' >> ~/.bashrc
RUN echo 'alias cbs="cd $COLCON_WS && colcon build --symlink-install --packages-select"' >> ~/.bashrc
RUN echo 'alias cbp="cd $COLCON_WS && colcon build --symlink-install --packages-up-to"' >> ~/.bashrc
RUN echo 'alias cbt="cd $COLCON_WS && colcon test --event-handlers console_direct+"' >> ~/.bashrc
RUN echo 'alias ctr="cd $COLCON_WS && colcon test-result"' >> ~/.bashrc

################################
## ADD ANY CUSTOM SETUP BELOW ##
################################
